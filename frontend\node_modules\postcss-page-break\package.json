{"name": "postcss-page-break", "version": "3.0.4", "description": "PostCSS plugin postcss-page-break to fallback `break-` properties with `page-break-` alias", "files": ["/index.js"], "scripts": {"test": "jest && npm run lint", "lint": "eslint *.js", "lint:fix": "eslint *.js --fix"}, "keywords": ["postcss", "css", "postcss-plugin", "break", "break-inside", "page-break-inside", "avoid"], "author": "shrpne <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/shrpne/postcss-page-break"}, "bugs": {"url": "https://github.com/shrpne/postcss-page-break/issues"}, "peerDependencies": {"postcss": "^8"}, "devDependencies": {"eslint": "^7.10.0", "eslint-config-google": "^0.14.0", "eslint-plugin-jest": "^24.0.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-security": "^1.4.0", "eslint-plugin-unicorn": "^22.0.0", "jest": "^26.4.2", "postcss": "^8.1.0"}}