from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from datetime import datetime, date, timedelta
from typing import List, Optional, Dict, Any

from database.models import Task, TaskStatus, TaskPriority

class TaskService:
    def __init__(self, db: Session):
        self.db = db

    def get_tasks(
        self,
        status: Optional[TaskStatus] = None,
        priority: Optional[TaskPriority] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Task]:
        """Get tasks with optional filtering"""
        query = self.db.query(Task)
        
        if status:
            query = query.filter(Task.status == status)
        if priority:
            query = query.filter(Task.priority == priority)
            
        return query.order_by(Task.created_at.desc()).offset(offset).limit(limit).all()

    def get_task(self, task_id: int) -> Optional[Task]:
        """Get a specific task by ID"""
        return self.db.query(Task).filter(Task.id == task_id).first()

    def create_task(self, task_data: Dict[str, Any]) -> Task:
        """Create a new task"""
        task = Task(**task_data)
        self.db.add(task)
        self.db.commit()
        self.db.refresh(task)
        return task

    def update_task(self, task_id: int, updates: Dict[str, Any]) -> Optional[Task]:
        """Update a task"""
        task = self.get_task(task_id)
        if not task:
            return None
            
        for key, value in updates.items():
            if hasattr(task, key):
                setattr(task, key, value)
        
        task.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(task)
        return task

    def update_task_status(self, task_id: int, status: TaskStatus) -> Optional[Task]:
        """Update task status"""
        updates = {"status": status}
        if status == TaskStatus.COMPLETED:
            updates["completed_at"] = datetime.utcnow()
        elif status == TaskStatus.PENDING:
            updates["completed_at"] = None
            
        return self.update_task(task_id, updates)

    def delete_task(self, task_id: int) -> bool:
        """Delete a task"""
        task = self.get_task(task_id)
        if not task:
            return False
            
        self.db.delete(task)
        self.db.commit()
        return True

    def get_today_tasks(self) -> List[Task]:
        """Get tasks due today"""
        today = date.today()
        return self.db.query(Task).filter(
            and_(
                Task.status == TaskStatus.PENDING,
                func.date(Task.due_date) == today
            )
        ).order_by(Task.priority.desc(), Task.created_at).all()

    def get_planned_tasks(self) -> List[Task]:
        """Get planned tasks (future due dates)"""
        today = date.today()
        return self.db.query(Task).filter(
            and_(
                Task.status == TaskStatus.PENDING,
                Task.due_date.isnot(None),
                func.date(Task.due_date) > today
            )
        ).order_by(Task.due_date, Task.priority.desc()).all()

    def get_completed_tasks(self, limit: int = 50) -> List[Task]:
        """Get completed tasks"""
        return self.db.query(Task).filter(
            Task.status == TaskStatus.COMPLETED
        ).order_by(Task.completed_at.desc()).limit(limit).all()

    def get_overdue_tasks(self) -> List[Task]:
        """Get overdue tasks"""
        today = date.today()
        return self.db.query(Task).filter(
            and_(
                Task.status == TaskStatus.PENDING,
                Task.due_date.isnot(None),
                func.date(Task.due_date) < today
            )
        ).order_by(Task.due_date).all()

    def search_tasks(self, query: str) -> List[Task]:
        """Search tasks by title or description"""
        search_term = f"%{query}%"
        return self.db.query(Task).filter(
            or_(
                Task.title.ilike(search_term),
                Task.description.ilike(search_term)
            )
        ).order_by(Task.created_at.desc()).all()

    def get_task_stats(self) -> Dict[str, Any]:
        """Get task statistics"""
        total_tasks = self.db.query(Task).count()
        pending_tasks = self.db.query(Task).filter(Task.status == TaskStatus.PENDING).count()
        completed_tasks = self.db.query(Task).filter(Task.status == TaskStatus.COMPLETED).count()
        overdue_tasks = len(self.get_overdue_tasks())
        today_tasks = len(self.get_today_tasks())
        
        # Completion rate
        completion_rate = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
        
        # Tasks by priority
        priority_stats = {}
        for priority in TaskPriority:
            count = self.db.query(Task).filter(
                and_(Task.priority == priority, Task.status == TaskStatus.PENDING)
            ).count()
            priority_stats[priority.value] = count
        
        return {
            "total_tasks": total_tasks,
            "pending_tasks": pending_tasks,
            "completed_tasks": completed_tasks,
            "overdue_tasks": overdue_tasks,
            "today_tasks": today_tasks,
            "completion_rate": round(completion_rate, 2),
            "priority_breakdown": priority_stats
        }

    def bulk_update_tasks(self, task_ids: List[int], updates: Dict[str, Any]) -> List[Task]:
        """Bulk update multiple tasks"""
        tasks = self.db.query(Task).filter(Task.id.in_(task_ids)).all()
        
        for task in tasks:
            for key, value in updates.items():
                if hasattr(task, key):
                    setattr(task, key, value)
            task.updated_at = datetime.utcnow()
        
        self.db.commit()
        return tasks

    def bulk_delete_tasks(self, task_ids: List[int]) -> int:
        """Bulk delete multiple tasks"""
        deleted_count = self.db.query(Task).filter(Task.id.in_(task_ids)).delete(synchronize_session=False)
        self.db.commit()
        return deleted_count

    def get_tasks_by_date_range(self, start_date: date, end_date: date) -> List[Task]:
        """Get tasks within a date range"""
        return self.db.query(Task).filter(
            and_(
                Task.due_date.isnot(None),
                func.date(Task.due_date) >= start_date,
                func.date(Task.due_date) <= end_date
            )
        ).order_by(Task.due_date).all()

    def get_recent_tasks(self, days: int = 7) -> List[Task]:
        """Get recently created tasks"""
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        return self.db.query(Task).filter(
            Task.created_at >= cutoff_date
        ).order_by(Task.created_at.desc()).all()
