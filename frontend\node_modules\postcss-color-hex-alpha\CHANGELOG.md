# Changes to PostCSS Color Hex Alpha

### 8.0.4 (June 10, 2022)

- Fixed: Issue with SVG hashes being interpreted as hex colors

### 8.0.3 (February 5, 2022)

- Improved `es module` and `commonjs` compatibility

### 8.0.2 (January 2, 2022)

- Removed Sourcemaps from package tarball.
- Moved CLI to CLI Package. See [announcement](https://github.com/csstools/postcss-plugins/discussions/121).

### 8.0.1 (December 16, 2021)

- Changed: now uses `postcss-value-parser` for parsing.
- Updated: documentation

### 8.0.0 (September 22, 2021)

- Updated: PostCSS Values Parser to v8 (major).
- Added missing `dist` to bundle.
- Added missing `exports` to `package.json`
- Added missing `types` to `package.json`
- Added bundling & testing as prepublish step.

### 7.0.0 (January 12, 2021)

- Updated: Support for PostCSS v8+

### 6.0.0 (April 25, 2020)

- Updated: `postcss` to 7.0.27 (patch).
- Updated: `postcss-values-parser` to 3.2.0 (major).
- Updated: Node support to 10.0.0 (major).
- Updated: Feature to use new percentage syntax.
- Removed: Support for the removed `gray()` function.

### 5.0.3 (March 30, 2019)

- Fixed: Issue with SVG hashes being interpretted as hex colors
- Updated: `postcss` to 7.0.14 (patch)
- Updated: `postcss-values-parser` to 2.0.1 (patch)

### 5.0.2 (September 18, 2018)

- Updated: PostCSS Values Parser 2 (patch for this project)

### 5.0.1 (September 18, 2018)

- Fixed: Issue correclty calculating each channel

### 5.0.0 (September 18, 2018)

- Initial version

### 4.0.0 (September 17, 2018)

- Updated: Support for PostCSS v7+
- Updated: Support for Node v6+
- Updated: color v3+

### 3.0.0 (May 15, 2017)

- Added: compatibility with postcss v6.x
- Updated dependencies

### 2.0.0 (September 8, 2015)

- Added: compatibility with postcss v5.x
- Removed: compatiblity with postcss v4.x

### 1.3.0 (August 13, 2015)

- Added: compatibility with postcss v4.1.x
([#3](https://github.com/postcss/postcss-color-hex-alpha/pull/3))

### 1.1.0 (November 25, 2014)

- Enhanced exceptions

### 1.0.0 - (October 4, 2014)

Initial release from [postcss-color](https://github.com/postcss/postcss-color)
