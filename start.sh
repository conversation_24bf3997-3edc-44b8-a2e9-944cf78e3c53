#!/bin/bash

echo "🧠 Starting BrainBuddy AI Personal Assistant..."

# Check AI Provider configuration
echo "🤖 Checking AI Provider..."
AI_PROVIDER=$(grep "^AI_PROVIDER=" .env 2>/dev/null | cut -d'=' -f2 | tr -d '"' || echo "ollama")
echo "   Current provider: $AI_PROVIDER"

if [ "$AI_PROVIDER" = "ollama" ]; then
    echo "🔍 Checking Ollama..."
    if ! curl -s http://localhost:11434/api/tags > /dev/null; then
        echo "⚠️  Ollama is not running. Please start Ollama first:"
        echo "   ollama serve"
        echo "   ollama pull mistral"
        echo ""
        echo "💡 Alternative: Switch to OpenAI by editing .env:"
        echo "   AI_PROVIDER=openai"
        echo "   OPENAI_API_KEY=your_api_key_here"
        exit 1
    fi
    echo "✅ Ollama is running"

elif [ "$AI_PROVIDER" = "openai" ]; then
    echo "🔍 Checking OpenAI configuration..."
    OPENAI_API_KEY=$(grep "^OPENAI_API_KEY=" .env 2>/dev/null | cut -d'=' -f2 | tr -d '"')

    if [ -z "$OPENAI_API_KEY" ] || [ "$OPENAI_API_KEY" = "your_openai_api_key_here" ]; then
        echo "⚠️  OpenAI API key not configured!"
        echo "   Please set your API key in .env file:"
        echo "   OPENAI_API_KEY=sk-your-actual-api-key"
        echo ""
        echo "💡 Get an API key from: https://platform.openai.com/api-keys"
        echo "💡 Alternative: Switch to Ollama (free, local):"
        echo "   AI_PROVIDER=ollama"
        exit 1
    fi
    echo "✅ OpenAI API key configured"

else
    echo "❌ Unknown AI provider: $AI_PROVIDER"
    echo "   Supported providers: ollama, openai"
    echo "   Please check your .env file"
    exit 1
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing frontend dependencies..."
    npm install
fi

if [ ! -d "backend/venv" ]; then
    echo "🐍 Creating Python virtual environment..."
    cd backend
    python -m venv venv
    source venv/bin/activate
    pip install -r requirements.txt
    cd ..
fi

# Start backend
echo "🚀 Starting backend server..."
cd backend
source venv/bin/activate
python main.py &
BACKEND_PID=$!
cd ..

# Wait for backend to start
echo "⏳ Waiting for backend to start..."
sleep 5

# Start frontend
echo "🎨 Starting frontend..."
cd frontend
npm install
npm start &
FRONTEND_PID=$!
cd ..

echo "✅ BrainBuddy is starting up!"
echo ""
echo "🌐 Access Points:"
echo "   📱 Frontend: http://localhost:3000"
echo "   🔧 Backend API: http://localhost:8000"
echo "   📚 API Docs: http://localhost:8000/docs"
echo ""
echo "🤖 AI Provider: $AI_PROVIDER"
if [ "$AI_PROVIDER" = "ollama" ]; then
    echo "   🏠 Running locally with Ollama"
    echo "   📋 Model: $(grep "^OLLAMA_MODEL=" .env 2>/dev/null | cut -d'=' -f2 | tr -d '"' || echo "mistral")"
elif [ "$AI_PROVIDER" = "openai" ]; then
    echo "   ☁️  Using OpenAI-compatible API"
    echo "   📋 Model: $(grep "^OPENAI_MODEL=" .env 2>/dev/null | cut -d'=' -f2 | tr -d '"' || echo "gpt-3.5-turbo")"
    echo "   🔗 Base URL: $(grep "^OPENAI_BASE_URL=" .env 2>/dev/null | cut -d'=' -f2 | tr -d '"' || echo "https://api.openai.com/v1")"
fi
echo ""
echo "💡 Tips:"
echo "   • Use the chat feature to interact with AI"
echo "   • Try natural language: 'Remind me to call John tomorrow'"
echo "   • Check AI provider status: curl http://localhost:8000/ai/provider/status"
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for user interrupt
trap "echo '🛑 Stopping BrainBuddy...'; kill $BACKEND_PID $FRONTEND_PID; exit" INT
wait
