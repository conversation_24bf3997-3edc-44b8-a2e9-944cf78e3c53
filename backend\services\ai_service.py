import os
import json
import re
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import httpx
from sqlalchemy.orm import Session

from database.models import Task, Habit

class AIService:
    def __init__(self, db: Session):
        self.db = db
        self.ollama_base_url = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
        self.model = os.getenv("OLLAMA_MODEL", "mistral")

    async def _call_ollama(self, prompt: str, system_prompt: str = None) -> str:
        """Make a call to Ollama API"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                payload = {
                    "model": self.model,
                    "prompt": prompt,
                    "stream": False
                }
                
                if system_prompt:
                    payload["system"] = system_prompt
                
                response = await client.post(
                    f"{self.ollama_base_url}/api/generate",
                    json=payload
                )
                response.raise_for_status()
                
                result = response.json()
                return result.get("response", "")
        except Exception as e:
            print(f"Ollama API error: {e}")
            return "I'm sorry, I'm having trouble connecting to the AI service right now."

    async def process_chat_message(self, message: str, context: Dict[str, Any] = {}) -> Dict[str, Any]:
        """Process a chat message and return AI response with possible actions"""
        
        system_prompt = """You are BrainBuddy, an AI personal assistant specialized in productivity and task management. 
        You help users manage their tasks, habits, and schedule. You are friendly, helpful, and proactive.
        
        When users ask about tasks, habits, or productivity, provide helpful advice and suggestions.
        If a user wants to create a task or habit, extract the relevant information and suggest it.
        
        Always respond in a conversational, helpful tone. Keep responses concise but informative."""
        
        # Add context information to the prompt
        context_info = ""
        if context.get("tasks"):
            context_info += f"\nUser's recent tasks: {json.dumps(context['tasks'][:5])}"
        if context.get("current_time"):
            context_info += f"\nCurrent time: {context['current_time']}"
        
        full_prompt = f"{context_info}\n\nUser message: {message}\n\nRespond as BrainBuddy:"
        
        response_text = await self._call_ollama(full_prompt, system_prompt)
        
        # Analyze if the message contains actionable items
        actions = await self._extract_actions(message, response_text)
        
        return {
            "message": response_text,
            "actions": actions,
            "timestamp": datetime.utcnow().isoformat()
        }

    async def process_natural_language(self, text: str, context: Dict[str, Any] = {}) -> Dict[str, Any]:
        """Process natural language input and extract structured data"""
        
        system_prompt = """You are an AI that extracts structured task information from natural language.
        Extract task details like title, description, priority, due date from user input.
        
        Return a JSON object with these fields:
        - title: string (required)
        - description: string (optional)
        - priority: "low", "medium", or "high"
        - due_date: ISO date string (optional)
        - suggestions: any additional suggestions
        
        Examples:
        "Call dentist tomorrow morning" -> {"title": "Call dentist", "due_date": "2024-01-15T09:00:00", "priority": "medium"}
        "Buy groceries this weekend" -> {"title": "Buy groceries", "due_date": "2024-01-13T10:00:00", "priority": "low"}
        """
        
        prompt = f"Extract task information from: '{text}'\nReturn only valid JSON:"
        
        response = await self._call_ollama(prompt, system_prompt)
        
        try:
            # Try to extract JSON from the response
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                suggestions = json.loads(json_match.group())
                return {
                    "success": True,
                    "suggestions": suggestions,
                    "original_text": text
                }
        except json.JSONDecodeError:
            pass
        
        # Fallback: basic parsing
        return {
            "success": False,
            "suggestions": {"title": text, "priority": "medium"},
            "original_text": text,
            "error": "Could not parse natural language input"
        }

    async def get_suggestions(self, context: Dict[str, Any] = {}) -> List[str]:
        """Get AI suggestions based on current context"""
        
        system_prompt = """You are a productivity assistant. Based on the user's current tasks and habits, 
        suggest 3-5 helpful actions or questions they might want to ask. Keep suggestions short and actionable.
        
        Examples:
        - "What should I focus on today?"
        - "Create a task for tomorrow"
        - "Show my productivity insights"
        - "Plan my week"
        """
        
        context_info = ""
        if context.get("today_tasks"):
            context_info += f"Today's tasks: {len(context['today_tasks'])} tasks\n"
        if context.get("habits"):
            context_info += f"Active habits: {len(context['habits'])} habits\n"
        
        prompt = f"{context_info}\nSuggest 4 helpful actions (one per line, no numbers):"
        
        response = await self._call_ollama(prompt, system_prompt)
        
        # Parse suggestions from response
        suggestions = []
        for line in response.split('\n'):
            line = line.strip()
            if line and not line.startswith('-') and len(line) > 5:
                # Remove bullet points or numbers
                clean_line = re.sub(r'^[-•*\d\.]\s*', '', line)
                if clean_line:
                    suggestions.append(clean_line)
        
        return suggestions[:4]  # Return max 4 suggestions

    async def generate_weekly_plan(self, context: Dict[str, Any] = {}) -> Dict[str, Any]:
        """Generate a weekly plan based on tasks and preferences"""
        
        system_prompt = """You are a productivity planner. Create a weekly plan based on the user's pending tasks.
        Distribute tasks across the week considering priorities and due dates.
        
        Return a JSON object with days of the week and suggested tasks for each day."""
        
        tasks_info = ""
        if context.get("pending_tasks"):
            tasks_info = f"Pending tasks: {json.dumps(context['pending_tasks'][:10])}"
        
        prompt = f"{tasks_info}\n\nCreate a weekly plan (JSON format with days as keys):"
        
        response = await self._call_ollama(prompt, system_prompt)
        
        try:
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                plan = json.loads(json_match.group())
                return {
                    "success": True,
                    "plan": plan,
                    "generated_at": datetime.utcnow().isoformat()
                }
        except json.JSONDecodeError:
            pass
        
        return {
            "success": False,
            "message": "Could not generate weekly plan",
            "raw_response": response
        }

    async def get_motivational_feedback(self, context: Dict[str, Any] = {}) -> Dict[str, Any]:
        """Get motivational feedback based on user progress"""
        
        system_prompt = """You are a motivational coach. Provide encouraging and constructive feedback 
        based on the user's task completion and habit progress. Be positive but realistic."""
        
        stats_info = ""
        if context.get("task_stats"):
            stats = context["task_stats"]
            stats_info += f"Task completion rate: {stats.get('completion_rate', 0)}%\n"
            stats_info += f"Pending tasks: {stats.get('pending_tasks', 0)}\n"
        
        if context.get("habits_progress"):
            habits = context["habits_progress"]
            stats_info += f"Habits completed today: {habits.get('completed_today', 0)}/{habits.get('total_habits', 0)}\n"
        
        prompt = f"{stats_info}\nProvide motivational feedback (2-3 sentences):"
        
        response = await self._call_ollama(prompt, system_prompt)
        
        return {
            "message": response,
            "type": "motivation",
            "timestamp": datetime.utcnow().isoformat()
        }

    async def analyze_patterns(self, context: Dict[str, Any] = {}) -> Dict[str, Any]:
        """Analyze user patterns and provide insights"""
        
        system_prompt = """You are a productivity analyst. Analyze the user's task and habit patterns 
        to identify trends, bottlenecks, and opportunities for improvement."""
        
        analysis_data = json.dumps(context, default=str)
        prompt = f"Analyze these productivity patterns:\n{analysis_data}\n\nProvide insights and recommendations:"
        
        response = await self._call_ollama(prompt, system_prompt)
        
        return {
            "analysis": response,
            "timeframe": context.get("timeframe", "week"),
            "generated_at": datetime.utcnow().isoformat()
        }

    async def get_productivity_insights(self, context: Dict[str, Any] = {}) -> Dict[str, Any]:
        """Get comprehensive productivity insights"""
        
        system_prompt = """You are a productivity expert. Provide actionable insights and recommendations 
        based on the user's task management and habit tracking data."""
        
        insights_data = json.dumps(context, default=str)
        prompt = f"Provide productivity insights based on:\n{insights_data}\n\nGive 3-5 key insights with actionable recommendations:"
        
        response = await self._call_ollama(prompt, system_prompt)
        
        return {
            "insights": response,
            "generated_at": datetime.utcnow().isoformat(),
            "data_analyzed": {
                "tasks_analyzed": len(context.get("recent_tasks", [])),
                "habits_tracked": context.get("habits_progress", {}).get("total_habits", 0)
            }
        }

    async def smart_schedule_task(self, context: Dict[str, Any] = {}) -> Dict[str, Any]:
        """Use AI to intelligently schedule a task"""
        
        system_prompt = """You are a scheduling assistant. Based on the new task and existing tasks,
        suggest the best time to schedule the new task. Consider priorities, deadlines, and workload distribution."""
        
        scheduling_data = json.dumps(context, default=str)
        prompt = f"Schedule this task optimally:\n{scheduling_data}\n\nSuggest the best date and time (JSON format):"
        
        response = await self._call_ollama(prompt, system_prompt)
        
        try:
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                schedule = json.loads(json_match.group())
                return {
                    "success": True,
                    "scheduled_task": schedule,
                    "reasoning": "AI-optimized scheduling"
                }
        except json.JSONDecodeError:
            pass
        
        return {
            "success": False,
            "message": "Could not generate optimal schedule",
            "raw_response": response
        }

    async def get_habit_recommendations(self, context: Dict[str, Any] = {}) -> Dict[str, Any]:
        """Get AI-powered habit recommendations"""
        
        system_prompt = """You are a habit formation expert. Based on the user's current habits and task patterns,
        recommend 3-5 new habits that would improve their productivity and well-being."""
        
        habit_data = json.dumps(context, default=str)
        prompt = f"Recommend new habits based on:\n{habit_data}\n\nSuggest 3-5 beneficial habits with explanations:"
        
        response = await self._call_ollama(prompt, system_prompt)
        
        return {
            "recommendations": response,
            "generated_at": datetime.utcnow().isoformat()
        }

    async def analyze_habit_progress(self, context: Dict[str, Any] = {}) -> Dict[str, Any]:
        """Analyze habit progress with AI insights"""
        
        system_prompt = """You are a habit tracking analyst. Analyze the habit progress data and provide
        insights on performance, trends, and suggestions for improvement."""
        
        progress_data = json.dumps(context, default=str)
        prompt = f"Analyze this habit progress:\n{progress_data}\n\nProvide analysis and improvement suggestions:"
        
        response = await self._call_ollama(prompt, system_prompt)
        
        return {
            "analysis": response,
            "habit_id": context.get("habit", {}).get("id"),
            "timeframe": context.get("timeframe", "month"),
            "generated_at": datetime.utcnow().isoformat()
        }

    async def _extract_actions(self, user_message: str, ai_response: str) -> List[Dict[str, Any]]:
        """Extract possible actions from the conversation"""
        actions = []
        
        # Simple keyword-based action detection
        message_lower = user_message.lower()
        
        if any(word in message_lower for word in ["create", "add", "new task", "remind me"]):
            actions.append({
                "type": "create_task",
                "label": "Create this as a task",
                "data": {"text": user_message}
            })
        
        if any(word in message_lower for word in ["plan", "schedule", "organize"]):
            actions.append({
                "type": "plan_week",
                "label": "Generate weekly plan",
                "data": {}
            })
        
        return actions
