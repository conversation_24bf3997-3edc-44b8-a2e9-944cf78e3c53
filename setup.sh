#!/bin/bash

# BrainBuddy Setup Script
echo "🧠 BrainBuddy AI Personal Assistant Setup"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️${NC} $1"
}

print_error() {
    echo -e "${RED}❌${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ️${NC} $1"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo -e "\n${BLUE}🔍 Checking prerequisites...${NC}"

# Check Node.js
if command_exists node; then
    NODE_VERSION=$(node --version)
    print_status "Node.js found: $NODE_VERSION"
    
    # Check if version is 18 or higher
    NODE_MAJOR=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
    if [ "$NODE_MAJOR" -lt 18 ]; then
        print_warning "Node.js version 18+ recommended. Current: $NODE_VERSION"
    fi
else
    print_error "Node.js not found. Please install Node.js 18+ from https://nodejs.org/"
    exit 1
fi

# Check npm
if command_exists npm; then
    NPM_VERSION=$(npm --version)
    print_status "npm found: $NPM_VERSION"
else
    print_error "npm not found. Please install npm"
    exit 1
fi

# Check Python
if command_exists python3; then
    PYTHON_VERSION=$(python3 --version)
    print_status "Python found: $PYTHON_VERSION"
elif command_exists python; then
    PYTHON_VERSION=$(python --version)
    print_status "Python found: $PYTHON_VERSION"
else
    print_error "Python not found. Please install Python 3.9+ from https://python.org/"
    exit 1
fi

# Check pip
if command_exists pip3; then
    PIP_CMD="pip3"
elif command_exists pip; then
    PIP_CMD="pip"
else
    print_error "pip not found. Please install pip"
    exit 1
fi

print_status "pip found: $PIP_CMD"

# Check Ollama
if command_exists ollama; then
    print_status "Ollama found"
    
    # Check if Ollama is running
    if curl -s http://localhost:11434/api/tags >/dev/null 2>&1; then
        print_status "Ollama is running"
    else
        print_warning "Ollama is not running. Starting Ollama..."
        ollama serve &
        OLLAMA_PID=$!
        sleep 3
        
        if curl -s http://localhost:11434/api/tags >/dev/null 2>&1; then
            print_status "Ollama started successfully"
        else
            print_error "Failed to start Ollama. Please start it manually: ollama serve"
        fi
    fi
    
    # Check if mistral model is available
    if ollama list | grep -q mistral; then
        print_status "Mistral model found"
    else
        print_info "Downloading Mistral model (this may take a few minutes)..."
        ollama pull mistral
        if [ $? -eq 0 ]; then
            print_status "Mistral model downloaded successfully"
        else
            print_error "Failed to download Mistral model"
        fi
    fi
else
    print_error "Ollama not found. Please install Ollama from https://ollama.ai/"
    print_info "After installing Ollama, run: ollama pull mistral"
    exit 1
fi

# Setup environment file
echo -e "\n${BLUE}🔧 Setting up environment...${NC}"

if [ ! -f .env ]; then
    print_info "Creating .env file..."
    cp .env.example .env 2>/dev/null || cat > .env << 'EOF'
# Database
DATABASE_URL=sqlite:///./brainbuddy.db

# Telegram Bot (optional)
# TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
# TELEGRAM_CHAT_ID=your_telegram_chat_id_here

# Ollama
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=mistral

# API Settings
API_HOST=localhost
API_PORT=8000
DEBUG=True

# Security
SECRET_KEY=brainbuddy_secret_key_change_in_production
EOF
    print_status "Environment file created"
else
    print_status "Environment file already exists"
fi

# Install root dependencies
echo -e "\n${BLUE}📦 Installing root dependencies...${NC}"
if [ ! -d "node_modules" ]; then
    npm install
    if [ $? -eq 0 ]; then
        print_status "Root dependencies installed"
    else
        print_error "Failed to install root dependencies"
        exit 1
    fi
else
    print_status "Root dependencies already installed"
fi

# Install frontend dependencies
echo -e "\n${BLUE}🎨 Setting up frontend...${NC}"
cd frontend

if [ ! -d "node_modules" ]; then
    npm install
    if [ $? -eq 0 ]; then
        print_status "Frontend dependencies installed"
    else
        print_error "Failed to install frontend dependencies"
        exit 1
    fi
else
    print_status "Frontend dependencies already installed"
fi

cd ..

# Setup Python backend
echo -e "\n${BLUE}🐍 Setting up backend...${NC}"
cd backend

# Create virtual environment
if [ ! -d "venv" ]; then
    print_info "Creating Python virtual environment..."
    python3 -m venv venv || python -m venv venv
    if [ $? -eq 0 ]; then
        print_status "Virtual environment created"
    else
        print_error "Failed to create virtual environment"
        exit 1
    fi
else
    print_status "Virtual environment already exists"
fi

# Activate virtual environment and install dependencies
print_info "Installing Python dependencies..."
source venv/bin/activate

$PIP_CMD install -r requirements.txt
if [ $? -eq 0 ]; then
    print_status "Python dependencies installed"
else
    print_error "Failed to install Python dependencies"
    exit 1
fi

# Test backend setup
print_info "Testing backend setup..."
python test_setup.py
if [ $? -eq 0 ]; then
    print_status "Backend setup test passed"
else
    print_warning "Backend setup test had issues (check output above)"
fi

cd ..

# Test frontend setup
echo -e "\n${BLUE}🧪 Testing frontend setup...${NC}"
node test_frontend.js
if [ $? -eq 0 ]; then
    print_status "Frontend setup test passed"
else
    print_warning "Frontend setup test had issues (check output above)"
fi

# Create desktop shortcut (optional)
echo -e "\n${BLUE}🖥️ Creating shortcuts...${NC}"

# Make start scripts executable
chmod +x start.sh
print_status "Start script made executable"

# Final instructions
echo -e "\n${GREEN}🎉 Setup completed successfully!${NC}"
echo -e "\n${BLUE}🚀 To start BrainBuddy:${NC}"
echo "   ./start.sh"
echo ""
echo -e "${BLUE}📱 Access points:${NC}"
echo "   • Electron App: Launches automatically"
echo "   • Web Interface: http://localhost:3000"
echo "   • Backend API: http://localhost:8000"
echo "   • API Docs: http://localhost:8000/docs"
echo ""
echo -e "${BLUE}📱 Optional Telegram Bot Setup:${NC}"
echo "   1. Create a bot with @BotFather on Telegram"
echo "   2. Add your bot token to .env file"
echo "   3. Restart the application"
echo ""
echo -e "${BLUE}🛠️ Manual start commands:${NC}"
echo "   Backend:  cd backend && source venv/bin/activate && python main.py"
echo "   Frontend: cd frontend && npm start"
echo "   Electron: npm run electron:dev"
echo ""
echo -e "${YELLOW}💡 Tip:${NC} Use 'ollama serve' if you need to restart Ollama"

# Ask if user wants to start now
echo ""
read -p "Would you like to start BrainBuddy now? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_info "Starting BrainBuddy..."
    ./start.sh
fi
