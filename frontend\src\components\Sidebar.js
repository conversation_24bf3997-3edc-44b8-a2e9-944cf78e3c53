import React from 'react';
import { 
  Inbox, 
  Calendar, 
  CheckSquare, 
  Target, 
  MessageCircle, 
  Settings, 
  Menu,
  X,
  Brain,
  Zap
} from 'lucide-react';

const Sidebar = ({ currentView, setCurrentView, collapsed, setCollapsed }) => {
  const menuItems = [
    { id: 'inbox', label: 'Inbox', icon: Inbox, count: 2 },
    { id: 'today', label: 'Today', icon: Calendar },
    { id: 'planned', label: 'Planned', icon: CheckSquare },
    { id: 'completed', label: 'Completed', icon: CheckSquare },
    { id: 'habits', label: 'Habits', icon: Target },
    { id: 'chat', label: 'Chat', icon: MessageCircle },
  ];

  return (
    <div className={`fixed left-0 top-0 h-full bg-slate-800 border-r border-slate-700 transition-all duration-300 z-50 ${
      collapsed ? 'w-16' : 'w-64'
    }`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-slate-700">
        {!collapsed && (
          <div className="flex items-center space-x-2">
            <Brain className="w-8 h-8 text-primary-500" />
            <div>
              <h1 className="text-lg font-bold text-white">BrainBuddy</h1>
              <p className="text-xs text-slate-400">AI powered</p>
            </div>
          </div>
        )}
        <button
          onClick={() => setCollapsed(!collapsed)}
          className="p-2 rounded-lg hover:bg-slate-700 text-slate-400 hover:text-white transition-colors"
        >
          {collapsed ? <Menu className="w-5 h-5" /> : <X className="w-5 h-5" />}
        </button>
      </div>

      {/* Navigation */}
      <nav className="p-4 space-y-2">
        {menuItems.map((item) => {
          const Icon = item.icon;
          const isActive = currentView === item.id;
          
          return (
            <button
              key={item.id}
              onClick={() => setCurrentView(item.id)}
              className={`sidebar-item w-full ${isActive ? 'active' : ''}`}
              title={collapsed ? item.label : ''}
            >
              <Icon className="w-5 h-5 flex-shrink-0" />
              {!collapsed && (
                <>
                  <span className="flex-1 text-left">{item.label}</span>
                  {item.count && (
                    <span className="bg-primary-600 text-white text-xs px-2 py-1 rounded-full">
                      {item.count}
                    </span>
                  )}
                </>
              )}
            </button>
          );
        })}
      </nav>

      {/* AI Status */}
      {!collapsed && (
        <div className="absolute bottom-4 left-4 right-4">
          <div className="glass-effect rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-2">
              <Zap className="w-4 h-4 text-green-400" />
              <span className="text-sm font-medium text-green-400">AI Ready</span>
            </div>
            <p className="text-xs text-slate-400">
              Ollama connected and ready to assist
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default Sidebar;
