from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.orm import Session
from typing import Dict, Any
import json

from database.database import get_db
from services.telegram_service import TelegramService

router = APIRouter()

@router.post("/webhook")
async def telegram_webhook(request: Request, db: Session = Depends(get_db)):
    """Handle Telegram webhook updates"""
    try:
        update_data = await request.json()
        telegram_service = TelegramService(db)
        await telegram_service.handle_update(update_data)
        return {"status": "ok"}
    except Exception as e:
        print(f"Telegram webhook error: {e}")
        raise HTTPException(status_code=500, detail="Webhook processing failed")

@router.post("/send-message")
async def send_telegram_message(
    message: str,
    chat_id: str = None,
    db: Session = Depends(get_db)
):
    """Send a message via Telegram bot"""
    try:
        telegram_service = TelegramService(db)
        result = await telegram_service.send_message(message, chat_id)
        return {"success": True, "message_id": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to send message: {str(e)}")

@router.get("/bot-info")
async def get_bot_info():
    """Get Telegram bot information"""
    try:
        telegram_service = TelegramService(None)
        info = await telegram_service.get_bot_info()
        return info
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get bot info: {str(e)}")

@router.post("/set-webhook")
async def set_webhook(webhook_url: str):
    """Set Telegram webhook URL"""
    try:
        telegram_service = TelegramService(None)
        result = await telegram_service.set_webhook(webhook_url)
        return {"success": True, "result": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to set webhook: {str(e)}")

@router.delete("/webhook")
async def delete_webhook():
    """Delete Telegram webhook"""
    try:
        telegram_service = TelegramService(None)
        result = await telegram_service.delete_webhook()
        return {"success": True, "result": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete webhook: {str(e)}")
