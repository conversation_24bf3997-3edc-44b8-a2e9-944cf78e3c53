// API Configuration
export const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Task Status
export const TASK_STATUS = {
  PENDING: 'pending',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
};

// Task Priority
export const TASK_PRIORITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high'
};

// Habit Frequency
export const HABIT_FREQUENCY = {
  DAILY: 'daily',
  WEEKLY: 'weekly',
  CUSTOM: 'custom'
};

// Views
export const VIEWS = {
  INBOX: 'inbox',
  TODAY: 'today',
  PLANNED: 'planned',
  COMPLETED: 'completed',
  HABITS: 'habits',
  CHAT: 'chat',
  CALENDAR: 'calendar'
};

// Colors
export const PRIORITY_COLORS = {
  [TASK_PRIORITY.HIGH]: {
    bg: 'bg-red-500/20',
    text: 'text-red-400',
    border: 'border-red-500'
  },
  [TASK_PRIORITY.MEDIUM]: {
    bg: 'bg-yellow-500/20',
    text: 'text-yellow-400',
    border: 'border-yellow-500'
  },
  [TASK_PRIORITY.LOW]: {
    bg: 'bg-green-500/20',
    text: 'text-green-400',
    border: 'border-green-500'
  }
};

// Date Formats
export const DATE_FORMATS = {
  DISPLAY: 'MMM d, yyyy',
  INPUT: 'yyyy-MM-dd',
  TIME: 'HH:mm',
  DATETIME: 'MMM d, yyyy HH:mm'
};

// Local Storage Keys
export const STORAGE_KEYS = {
  THEME: 'brainbuddy_theme',
  SIDEBAR_COLLAPSED: 'brainbuddy_sidebar_collapsed',
  USER_PREFERENCES: 'brainbuddy_preferences'
};

// Notification Types
export const NOTIFICATION_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info'
};

// AI Chat Message Types
export const MESSAGE_TYPES = {
  USER: 'user',
  AI: 'ai',
  SYSTEM: 'system'
};

// Default Values
export const DEFAULTS = {
  TASK_PRIORITY: TASK_PRIORITY.MEDIUM,
  HABIT_FREQUENCY: HABIT_FREQUENCY.DAILY,
  PAGINATION_LIMIT: 50,
  CHAT_MESSAGE_LIMIT: 100
};

// Feature Flags
export const FEATURES = {
  TELEGRAM_BOT: true,
  AI_CHAT: true,
  HABITS: true,
  CALENDAR: true,
  ANALYTICS: true
};
