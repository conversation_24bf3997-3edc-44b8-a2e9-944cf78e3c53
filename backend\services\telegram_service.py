import os
import json
import async<PERSON>
from typing import Dict, Any, Optional
import httpx
from sqlalchemy.orm import Session

from services.task_service import TaskService
from services.habit_service import HabitService
from services.ai_service import AIService

class TelegramService:
    def __init__(self, db: Session):
        self.db = db
        self.bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
        self.chat_id = os.getenv("TELEGRAM_CHAT_ID")
        self.base_url = f"https://api.telegram.org/bot{self.bot_token}"
        
        if not self.bot_token:
            print("⚠️  TELEGRAM_BOT_TOKEN not set in environment variables")

    async def handle_update(self, update: Dict[str, Any]):
        """Handle incoming Telegram update"""
        if not self.bot_token:
            return
        
        try:
            if "message" in update:
                await self._handle_message(update["message"])
            elif "callback_query" in update:
                await self._handle_callback_query(update["callback_query"])
        except Exception as e:
            print(f"Error handling Telegram update: {e}")

    async def _handle_message(self, message: Dict[str, Any]):
        """Handle incoming message"""
        chat_id = message["chat"]["id"]
        text = message.get("text", "")
        
        if text.startswith("/"):
            await self._handle_command(chat_id, text)
        else:
            await self._handle_natural_language(chat_id, text)

    async def _handle_command(self, chat_id: int, command: str):
        """Handle bot commands"""
        command_parts = command.split(" ", 1)
        cmd = command_parts[0].lower()
        args = command_parts[1] if len(command_parts) > 1 else ""

        if cmd == "/start":
            await self._send_welcome_message(chat_id)
        elif cmd == "/help":
            await self._send_help_message(chat_id)
        elif cmd == "/newtask":
            await self._create_task_from_command(chat_id, args)
        elif cmd == "/list":
            await self._list_tasks(chat_id)
        elif cmd == "/today":
            await self._list_today_tasks(chat_id)
        elif cmd == "/done":
            await self._complete_task(chat_id, args)
        elif cmd == "/habits":
            await self._list_habits(chat_id)
        elif cmd == "/stats":
            await self._send_stats(chat_id)
        else:
            await self.send_message("Unknown command. Type /help for available commands.", chat_id)

    async def _handle_natural_language(self, chat_id: int, text: str):
        """Handle natural language input"""
        try:
            ai_service = AIService(self.db)
            
            # Get context
            task_service = TaskService(self.db)
            recent_tasks = task_service.get_recent_tasks(days=3)
            
            context = {
                "platform": "telegram",
                "recent_tasks": [task.to_dict() for task in recent_tasks[:5]]
            }
            
            # Process with AI
            response = await ai_service.process_chat_message(text, context)
            
            # Send AI response
            await self.send_message(response["message"], chat_id)
            
            # Handle any actions
            if response.get("actions"):
                for action in response["actions"]:
                    if action["type"] == "create_task":
                        # Extract task from natural language
                        nl_result = await ai_service.process_natural_language(text)
                        if nl_result["success"]:
                            task_data = nl_result["suggestions"]
                            task_service = TaskService(self.db)
                            task = task_service.create_task(task_data)
                            await self.send_message(f"✅ Created task: {task.title}", chat_id)
                        
        except Exception as e:
            print(f"Error processing natural language: {e}")
            await self.send_message("I'm having trouble understanding that. Try using a command like /newtask or /help.", chat_id)

    async def _send_welcome_message(self, chat_id: int):
        """Send welcome message"""
        message = """🧠 Welcome to BrainBuddy!

I'm your AI-powered personal assistant. I can help you:

📋 Manage tasks
🎯 Track habits  
📊 Get productivity insights
🤖 Answer questions with AI

Type /help to see all available commands, or just talk to me naturally!"""
        
        await self.send_message(message, chat_id)

    async def _send_help_message(self, chat_id: int):
        """Send help message"""
        message = """🤖 BrainBuddy Commands:

📋 **Tasks**
/newtask <description> - Create a new task
/list - Show all pending tasks
/today - Show today's tasks
/done <task_id> - Mark task as completed

🎯 **Habits**
/habits - Show your habits
/stats - Show productivity statistics

💬 **AI Chat**
Just type naturally! I can:
- Create tasks from your messages
- Answer productivity questions
- Give motivational feedback
- Provide insights and suggestions

Examples:
"Remind me to call John tomorrow"
"What should I focus on today?"
"How am I doing with my habits?"
"""
        
        await self.send_message(message, chat_id)

    async def _create_task_from_command(self, chat_id: int, description: str):
        """Create task from /newtask command"""
        if not description:
            await self.send_message("Please provide a task description. Example: /newtask Buy groceries", chat_id)
            return
        
        try:
            # Use AI to enhance the task
            ai_service = AIService(self.db)
            nl_result = await ai_service.process_natural_language(description)
            
            task_data = nl_result.get("suggestions", {"title": description})
            
            task_service = TaskService(self.db)
            task = task_service.create_task(task_data)
            
            message = f"✅ Task created: {task.title}"
            if task.due_date:
                message += f"\n📅 Due: {task.due_date.strftime('%Y-%m-%d %H:%M')}"
            if task.priority != "medium":
                message += f"\n🏷️ Priority: {task.priority}"
            
            await self.send_message(message, chat_id)
            
        except Exception as e:
            print(f"Error creating task: {e}")
            await self.send_message("Failed to create task. Please try again.", chat_id)

    async def _list_tasks(self, chat_id: int):
        """List all pending tasks"""
        try:
            task_service = TaskService(self.db)
            tasks = task_service.get_tasks(status="pending", limit=10)
            
            if not tasks:
                await self.send_message("📝 No pending tasks! You're all caught up! 🎉", chat_id)
                return
            
            message = "📋 **Your Tasks:**\n\n"
            for i, task in enumerate(tasks, 1):
                message += f"{i}. {task.title}"
                if task.due_date:
                    message += f" (Due: {task.due_date.strftime('%m/%d')})"
                if task.priority == "high":
                    message += " 🔴"
                elif task.priority == "medium":
                    message += " 🟡"
                message += f" [ID: {task.id}]\n"
            
            message += f"\nTotal: {len(tasks)} tasks"
            await self.send_message(message, chat_id)
            
        except Exception as e:
            print(f"Error listing tasks: {e}")
            await self.send_message("Failed to retrieve tasks.", chat_id)

    async def _list_today_tasks(self, chat_id: int):
        """List today's tasks"""
        try:
            task_service = TaskService(self.db)
            tasks = task_service.get_today_tasks()
            
            if not tasks:
                await self.send_message("📅 No tasks scheduled for today! 🎉", chat_id)
                return
            
            message = "📅 **Today's Tasks:**\n\n"
            for i, task in enumerate(tasks, 1):
                message += f"{i}. {task.title}"
                if task.priority == "high":
                    message += " 🔴"
                elif task.priority == "medium":
                    message += " 🟡"
                message += f" [ID: {task.id}]\n"
            
            await self.send_message(message, chat_id)
            
        except Exception as e:
            print(f"Error listing today's tasks: {e}")
            await self.send_message("Failed to retrieve today's tasks.", chat_id)

    async def _complete_task(self, chat_id: int, task_id_str: str):
        """Complete a task"""
        if not task_id_str:
            await self.send_message("Please provide a task ID. Example: /done 123", chat_id)
            return
        
        try:
            task_id = int(task_id_str)
            task_service = TaskService(self.db)
            task = task_service.update_task_status(task_id, "completed")
            
            if task:
                await self.send_message(f"✅ Completed: {task.title}", chat_id)
            else:
                await self.send_message("Task not found. Use /list to see task IDs.", chat_id)
                
        except ValueError:
            await self.send_message("Invalid task ID. Please provide a number.", chat_id)
        except Exception as e:
            print(f"Error completing task: {e}")
            await self.send_message("Failed to complete task.", chat_id)

    async def _list_habits(self, chat_id: int):
        """List habits and their status"""
        try:
            habit_service = HabitService(self.db)
            habits = habit_service.get_habits()
            
            if not habits:
                await self.send_message("🎯 No habits tracked yet. Create some in the app!", chat_id)
                return
            
            message = "🎯 **Your Habits:**\n\n"
            for habit in habits:
                completed_today = habit_service.is_completed_today(habit.id)
                status = "✅" if completed_today else "⏳"
                message += f"{status} {habit.name} (Streak: {habit.current_streak})\n"
            
            await self.send_message(message, chat_id)
            
        except Exception as e:
            print(f"Error listing habits: {e}")
            await self.send_message("Failed to retrieve habits.", chat_id)

    async def _send_stats(self, chat_id: int):
        """Send productivity statistics"""
        try:
            task_service = TaskService(self.db)
            habit_service = HabitService(self.db)
            
            task_stats = task_service.get_task_stats()
            habits_progress = habit_service.get_all_habits_progress("week")
            
            message = "📊 **Your Stats:**\n\n"
            message += f"📋 Tasks: {task_stats['completed_tasks']}/{task_stats['total_tasks']} completed\n"
            message += f"📈 Completion Rate: {task_stats['completion_rate']}%\n"
            message += f"⏰ Today's Tasks: {task_stats['today_tasks']}\n"
            message += f"⚠️ Overdue: {task_stats['overdue_tasks']}\n\n"
            
            message += f"🎯 Habits: {habits_progress['completed_today']}/{habits_progress['total_habits']} completed today\n"
            message += f"📊 Weekly Average: {habits_progress['average_completion_rate']}%"
            
            await self.send_message(message, chat_id)
            
        except Exception as e:
            print(f"Error getting stats: {e}")
            await self.send_message("Failed to retrieve statistics.", chat_id)

    async def send_message(self, text: str, chat_id: str = None) -> Optional[int]:
        """Send a message via Telegram"""
        if not self.bot_token:
            print(f"Would send Telegram message: {text}")
            return None
        
        target_chat_id = chat_id or self.chat_id
        if not target_chat_id:
            print("No chat ID available for sending message")
            return None
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/sendMessage",
                    json={
                        "chat_id": target_chat_id,
                        "text": text,
                        "parse_mode": "Markdown"
                    }
                )
                response.raise_for_status()
                result = response.json()
                return result["result"]["message_id"]
        except Exception as e:
            print(f"Failed to send Telegram message: {e}")
            return None

    async def get_bot_info(self) -> Dict[str, Any]:
        """Get bot information"""
        if not self.bot_token:
            return {"error": "Bot token not configured"}
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.base_url}/getMe")
                response.raise_for_status()
                return response.json()
        except Exception as e:
            return {"error": str(e)}

    async def set_webhook(self, webhook_url: str) -> Dict[str, Any]:
        """Set webhook URL"""
        if not self.bot_token:
            return {"error": "Bot token not configured"}
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/setWebhook",
                    json={"url": webhook_url}
                )
                response.raise_for_status()
                return response.json()
        except Exception as e:
            return {"error": str(e)}

    async def delete_webhook(self) -> Dict[str, Any]:
        """Delete webhook"""
        if not self.bot_token:
            return {"error": "Bot token not configured"}
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(f"{self.base_url}/deleteWebhook")
                response.raise_for_status()
                return response.json()
        except Exception as e:
            return {"error": str(e)}

# Initialize Telegram bot
async def init_telegram_bot():
    """Initialize Telegram bot"""
    try:
        telegram_service = TelegramService(None)
        if telegram_service.bot_token:
            info = await telegram_service.get_bot_info()
            if "result" in info:
                print(f"✅ Telegram bot connected: @{info['result']['username']}")
                return True
        return False
    except Exception as e:
        print(f"Failed to initialize Telegram bot: {e}")
        return False
