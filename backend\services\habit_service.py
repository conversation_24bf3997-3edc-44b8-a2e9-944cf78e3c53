from sqlalchemy.orm import Session
from sqlalchemy import and_, func
from datetime import datetime, date, timedelta
from typing import List, Optional, Dict, Any

from database.models import Habit, HabitCompletion, HabitFrequency

class HabitService:
    def __init__(self, db: Session):
        self.db = db

    def get_habits(self, active_only: bool = True) -> List[Habit]:
        """Get all habits"""
        query = self.db.query(Habit)
        if active_only:
            query = query.filter(Habit.is_active == True)
        return query.order_by(Habit.created_at.desc()).all()

    def get_habit(self, habit_id: int) -> Optional[Habit]:
        """Get a specific habit by ID"""
        return self.db.query(Habit).filter(Habit.id == habit_id).first()

    def create_habit(self, habit_data: Dict[str, Any]) -> Habit:
        """Create a new habit"""
        habit = Habit(**habit_data)
        self.db.add(habit)
        self.db.commit()
        self.db.refresh(habit)
        return habit

    def update_habit(self, habit_id: int, updates: Dict[str, Any]) -> Optional[Habit]:
        """Update a habit"""
        habit = self.get_habit(habit_id)
        if not habit:
            return None
            
        for key, value in updates.items():
            if hasattr(habit, key):
                setattr(habit, key, value)
        
        habit.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(habit)
        return habit

    def delete_habit(self, habit_id: int) -> bool:
        """Delete a habit"""
        habit = self.get_habit(habit_id)
        if not habit:
            return False
            
        # Delete all completions first
        self.db.query(HabitCompletion).filter(HabitCompletion.habit_id == habit_id).delete()
        self.db.delete(habit)
        self.db.commit()
        return True

    def complete_habit(self, habit_id: int, notes: Optional[str] = None) -> Optional[HabitCompletion]:
        """Mark habit as completed"""
        habit = self.get_habit(habit_id)
        if not habit:
            return None
        
        # Create completion record
        completion = HabitCompletion(
            habit_id=habit_id,
            notes=notes,
            completed_at=datetime.utcnow()
        )
        self.db.add(completion)
        
        # Update habit statistics
        habit.total_completions += 1
        
        # Update streak
        if self.is_completed_yesterday(habit_id) or habit.current_streak == 0:
            habit.current_streak += 1
        else:
            habit.current_streak = 1
        
        # Update longest streak
        if habit.current_streak > habit.longest_streak:
            habit.longest_streak = habit.current_streak
        
        habit.updated_at = datetime.utcnow()
        
        self.db.commit()
        self.db.refresh(completion)
        return completion

    def is_completed_today(self, habit_id: int) -> bool:
        """Check if habit is completed today"""
        today = date.today()
        completion = self.db.query(HabitCompletion).filter(
            and_(
                HabitCompletion.habit_id == habit_id,
                func.date(HabitCompletion.completed_at) == today
            )
        ).first()
        return completion is not None

    def is_completed_yesterday(self, habit_id: int) -> bool:
        """Check if habit was completed yesterday"""
        yesterday = date.today() - timedelta(days=1)
        completion = self.db.query(HabitCompletion).filter(
            and_(
                HabitCompletion.habit_id == habit_id,
                func.date(HabitCompletion.completed_at) == yesterday
            )
        ).first()
        return completion is not None

    def get_habit_progress(self, habit_id: int, timeframe: str = "month") -> Dict[str, Any]:
        """Get habit progress for a specific timeframe"""
        habit = self.get_habit(habit_id)
        if not habit:
            return {}
        
        # Calculate date range
        today = date.today()
        if timeframe == "week":
            start_date = today - timedelta(days=7)
        elif timeframe == "month":
            start_date = today - timedelta(days=30)
        elif timeframe == "year":
            start_date = today - timedelta(days=365)
        else:
            start_date = today - timedelta(days=30)
        
        # Get completions in timeframe
        completions = self.db.query(HabitCompletion).filter(
            and_(
                HabitCompletion.habit_id == habit_id,
                func.date(HabitCompletion.completed_at) >= start_date
            )
        ).all()
        
        # Calculate completion rate
        total_days = (today - start_date).days
        completed_days = len(set(completion.completed_at.date() for completion in completions))
        completion_rate = (completed_days / total_days * 100) if total_days > 0 else 0
        
        # Group completions by date
        completion_dates = [completion.completed_at.date() for completion in completions]
        daily_progress = {}
        
        current_date = start_date
        while current_date <= today:
            daily_progress[current_date.isoformat()] = current_date in completion_dates
            current_date += timedelta(days=1)
        
        return {
            "habit_id": habit_id,
            "timeframe": timeframe,
            "total_days": total_days,
            "completed_days": completed_days,
            "completion_rate": round(completion_rate, 2),
            "current_streak": habit.current_streak,
            "longest_streak": habit.longest_streak,
            "daily_progress": daily_progress
        }

    def get_habit_stats(self, habit_id: int) -> Dict[str, Any]:
        """Get detailed habit statistics"""
        habit = self.get_habit(habit_id)
        if not habit:
            return {}
        
        # Basic stats
        total_completions = habit.total_completions
        current_streak = habit.current_streak
        longest_streak = habit.longest_streak
        
        # Days since creation
        days_since_creation = (datetime.utcnow().date() - habit.created_at.date()).days
        
        # Overall completion rate
        overall_completion_rate = (total_completions / days_since_creation * 100) if days_since_creation > 0 else 0
        
        # Recent performance (last 7 days)
        recent_progress = self.get_habit_progress(habit_id, "week")
        
        # Best and worst months
        # This would require more complex queries - simplified for now
        
        return {
            "habit_id": habit_id,
            "total_completions": total_completions,
            "current_streak": current_streak,
            "longest_streak": longest_streak,
            "days_since_creation": days_since_creation,
            "overall_completion_rate": round(overall_completion_rate, 2),
            "recent_completion_rate": recent_progress.get("completion_rate", 0),
            "completed_today": self.is_completed_today(habit_id)
        }

    def get_habit_history(self, habit_id: int, limit: int = 30) -> List[HabitCompletion]:
        """Get habit completion history"""
        return self.db.query(HabitCompletion).filter(
            HabitCompletion.habit_id == habit_id
        ).order_by(HabitCompletion.completed_at.desc()).limit(limit).all()

    def reset_habit_streak(self, habit_id: int) -> Optional[Habit]:
        """Reset habit streak"""
        habit = self.get_habit(habit_id)
        if not habit:
            return None
        
        habit.current_streak = 0
        habit.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(habit)
        return habit

    def get_all_habits_progress(self, timeframe: str = "week") -> Dict[str, Any]:
        """Get progress for all active habits"""
        habits = self.get_habits(active_only=True)
        
        progress_data = []
        for habit in habits:
            progress = self.get_habit_progress(habit.id, timeframe)
            progress_data.append({
                "habit": habit.to_dict(),
                "progress": progress
            })
        
        # Calculate overall statistics
        total_habits = len(habits)
        completed_today = sum(1 for habit in habits if self.is_completed_today(habit.id))
        avg_completion_rate = sum(p["progress"].get("completion_rate", 0) for p in progress_data) / total_habits if total_habits > 0 else 0
        
        return {
            "timeframe": timeframe,
            "total_habits": total_habits,
            "completed_today": completed_today,
            "average_completion_rate": round(avg_completion_rate, 2),
            "habits_progress": progress_data
        }
