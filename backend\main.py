import os
import uvicorn
from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager
from dotenv import load_dotenv

from database.database import init_db
from routers import tasks, habits, ai, telegram_bot, calendar
from services.scheduler import start_scheduler

# Load environment variables
load_dotenv()

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    print("🚀 Starting BrainBuddy AI Assistant...")
    
    # Initialize database
    await init_db()
    print("✅ Database initialized")
    
    # Start scheduler for background tasks
    start_scheduler()
    print("✅ Scheduler started")
    
    # Initialize Telegram bot
    try:
        from services.telegram_service import init_telegram_bot
        await init_telegram_bot()
        print("✅ Telegram bot initialized")
    except Exception as e:
        print(f"⚠️  Telegram bot initialization failed: {e}")
    
    print("🎉 BrainBuddy is ready!")
    
    yield
    
    # Shutdown
    print("👋 Shutting down BrainBuddy...")

# Create FastAPI app
app = Fast<PERSON>I(
    title="BrainBuddy AI Assistant",
    description="AI-powered personal assistant for task management and productivity",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(tasks.router, prefix="/tasks", tags=["tasks"])
app.include_router(habits.router, prefix="/habits", tags=["habits"])
app.include_router(ai.router, prefix="/ai", tags=["ai"])
app.include_router(telegram_bot.router, prefix="/telegram", tags=["telegram"])
app.include_router(calendar.router, prefix="/calendar", tags=["calendar"])

@app.get("/")
async def root():
    return {
        "message": "BrainBuddy AI Assistant API",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "database": "connected",
        "ai": "ready"
    }

if __name__ == "__main__":
    host = os.getenv("API_HOST", "localhost")
    port = int(os.getenv("API_PORT", 8000))
    debug = os.getenv("DEBUG", "True").lower() == "true"
    
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=debug,
        log_level="info"
    )
