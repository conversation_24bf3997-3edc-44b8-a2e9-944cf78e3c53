from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, date
from pydantic import BaseModel

from database.database import get_db
from database.models import Task, TaskStatus, TaskPriority
from services.task_service import TaskService

router = APIRouter()

# Pydantic models
class TaskCreate(BaseModel):
    title: str
    description: Optional[str] = None
    priority: Optional[TaskPriority] = TaskPriority.MEDIUM
    due_date: Optional[datetime] = None
    status: Optional[TaskStatus] = TaskStatus.PENDING

class TaskUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    priority: Optional[TaskPriority] = None
    due_date: Optional[datetime] = None
    status: Optional[TaskStatus] = None
    completed_at: Optional[datetime] = None

class TaskResponse(BaseModel):
    id: int
    title: str
    description: Optional[str]
    status: TaskStatus
    priority: TaskPriority
    due_date: Optional[datetime]
    completed_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    ai_generated: bool
    ai_suggestions: Optional[str]

    class Config:
        from_attributes = True

@router.get("/", response_model=List[TaskResponse])
async def get_tasks(
    status: Optional[TaskStatus] = None,
    priority: Optional[TaskPriority] = None,
    limit: int = Query(100, le=1000),
    offset: int = Query(0, ge=0),
    db: Session = Depends(get_db)
):
    """Get all tasks with optional filtering"""
    task_service = TaskService(db)
    tasks = task_service.get_tasks(
        status=status,
        priority=priority,
        limit=limit,
        offset=offset
    )
    return [TaskResponse.from_orm(task) for task in tasks]

@router.get("/today", response_model=List[TaskResponse])
async def get_today_tasks(db: Session = Depends(get_db)):
    """Get tasks due today"""
    task_service = TaskService(db)
    tasks = task_service.get_today_tasks()
    return [TaskResponse.from_orm(task) for task in tasks]

@router.get("/planned", response_model=List[TaskResponse])
async def get_planned_tasks(db: Session = Depends(get_db)):
    """Get planned tasks (future due dates)"""
    task_service = TaskService(db)
    tasks = task_service.get_planned_tasks()
    return [TaskResponse.from_orm(task) for task in tasks]

@router.get("/completed", response_model=List[TaskResponse])
async def get_completed_tasks(
    limit: int = Query(50, le=200),
    db: Session = Depends(get_db)
):
    """Get completed tasks"""
    task_service = TaskService(db)
    tasks = task_service.get_completed_tasks(limit=limit)
    return [TaskResponse.from_orm(task) for task in tasks]

@router.get("/search", response_model=List[TaskResponse])
async def search_tasks(
    q: str = Query(..., min_length=1),
    db: Session = Depends(get_db)
):
    """Search tasks by title or description"""
    task_service = TaskService(db)
    tasks = task_service.search_tasks(q)
    return [TaskResponse.from_orm(task) for task in tasks]

@router.get("/stats")
async def get_task_stats(db: Session = Depends(get_db)):
    """Get task statistics"""
    task_service = TaskService(db)
    return task_service.get_task_stats()

@router.get("/{task_id}", response_model=TaskResponse)
async def get_task(task_id: int, db: Session = Depends(get_db)):
    """Get a specific task"""
    task_service = TaskService(db)
    task = task_service.get_task(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    return TaskResponse.from_orm(task)

@router.post("/", response_model=TaskResponse)
async def create_task(task_data: TaskCreate, db: Session = Depends(get_db)):
    """Create a new task"""
    task_service = TaskService(db)
    task = task_service.create_task(task_data.dict())
    return TaskResponse.from_orm(task)

@router.put("/{task_id}", response_model=TaskResponse)
async def update_task(
    task_id: int,
    task_data: TaskUpdate,
    db: Session = Depends(get_db)
):
    """Update a task"""
    task_service = TaskService(db)
    task = task_service.update_task(task_id, task_data.dict(exclude_unset=True))
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    return TaskResponse.from_orm(task)

@router.patch("/{task_id}/status")
async def update_task_status(
    task_id: int,
    status: TaskStatus,
    db: Session = Depends(get_db)
):
    """Update task status"""
    task_service = TaskService(db)
    task = task_service.update_task_status(task_id, status)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    return {"message": "Task status updated", "task": TaskResponse.from_orm(task)}

@router.delete("/{task_id}")
async def delete_task(task_id: int, db: Session = Depends(get_db)):
    """Delete a task"""
    task_service = TaskService(db)
    success = task_service.delete_task(task_id)
    if not success:
        raise HTTPException(status_code=404, detail="Task not found")
    return {"message": "Task deleted successfully"}

# Bulk operations
@router.patch("/bulk")
async def bulk_update_tasks(
    task_ids: List[int],
    updates: TaskUpdate,
    db: Session = Depends(get_db)
):
    """Bulk update tasks"""
    task_service = TaskService(db)
    updated_tasks = task_service.bulk_update_tasks(task_ids, updates.dict(exclude_unset=True))
    return {
        "message": f"Updated {len(updated_tasks)} tasks",
        "tasks": [TaskResponse.from_orm(task) for task in updated_tasks]
    }

@router.delete("/bulk")
async def bulk_delete_tasks(
    task_ids: List[int],
    db: Session = Depends(get_db)
):
    """Bulk delete tasks"""
    task_service = TaskService(db)
    deleted_count = task_service.bulk_delete_tasks(task_ids)
    return {"message": f"Deleted {deleted_count} tasks"}
