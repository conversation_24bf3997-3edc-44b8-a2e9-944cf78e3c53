@echo off
setlocal enabledelayedexpansion

REM BrainBuddy Setup Script for Windows
echo 🧠 BrainBuddy AI Personal Assistant Setup
echo ==========================================

REM Check Node.js
echo.
echo 🔍 Checking prerequisites...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js not found. Please install Node.js 18+ from https://nodejs.org/
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo ✅ Node.js found: !NODE_VERSION!
)

REM Check npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm not found. Please install npm
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
    echo ✅ npm found: !NPM_VERSION!
)

REM Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found. Please install Python 3.9+ from https://python.org/
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('python --version') do set PYTHON_VERSION=%%i
    echo ✅ Python found: !PYTHON_VERSION!
)

REM Check pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip not found. Please install pip
    pause
    exit /b 1
) else (
    echo ✅ pip found
)

REM Check Ollama
ollama --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Ollama not found. Please install Ollama from https://ollama.ai/
    echo 💡 After installing Ollama, run: ollama pull mistral
    pause
    exit /b 1
) else (
    echo ✅ Ollama found
    
    REM Check if Ollama is running
    curl -s http://localhost:11434/api/tags >nul 2>&1
    if errorlevel 1 (
        echo ⚠️  Ollama is not running. Please start Ollama first:
        echo    ollama serve
        echo    Then run this setup script again.
        pause
        exit /b 1
    ) else (
        echo ✅ Ollama is running
    )
    
    REM Check if mistral model is available
    ollama list | findstr mistral >nul 2>&1
    if errorlevel 1 (
        echo 📥 Downloading Mistral model (this may take a few minutes)...
        ollama pull mistral
        if errorlevel 1 (
            echo ❌ Failed to download Mistral model
            pause
            exit /b 1
        ) else (
            echo ✅ Mistral model downloaded successfully
        )
    ) else (
        echo ✅ Mistral model found
    )
)

REM Setup environment file
echo.
echo 🔧 Setting up environment...
if not exist ".env" (
    echo 📝 Creating .env file...
    (
        echo # Database
        echo DATABASE_URL=sqlite:///./brainbuddy.db
        echo.
        echo # Telegram Bot ^(optional^)
        echo # TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
        echo # TELEGRAM_CHAT_ID=your_telegram_chat_id_here
        echo.
        echo # Ollama
        echo OLLAMA_BASE_URL=http://localhost:11434
        echo OLLAMA_MODEL=mistral
        echo.
        echo # API Settings
        echo API_HOST=localhost
        echo API_PORT=8000
        echo DEBUG=True
        echo.
        echo # Security
        echo SECRET_KEY=brainbuddy_secret_key_change_in_production
    ) > .env
    echo ✅ Environment file created
) else (
    echo ✅ Environment file already exists
)

REM Install root dependencies
echo.
echo 📦 Installing root dependencies...
if not exist "node_modules" (
    npm install
    if errorlevel 1 (
        echo ❌ Failed to install root dependencies
        pause
        exit /b 1
    ) else (
        echo ✅ Root dependencies installed
    )
) else (
    echo ✅ Root dependencies already installed
)

REM Install frontend dependencies
echo.
echo 🎨 Setting up frontend...
cd frontend
if not exist "node_modules" (
    npm install
    if errorlevel 1 (
        echo ❌ Failed to install frontend dependencies
        pause
        exit /b 1
    ) else (
        echo ✅ Frontend dependencies installed
    )
) else (
    echo ✅ Frontend dependencies already installed
)
cd ..

REM Setup Python backend
echo.
echo 🐍 Setting up backend...
cd backend

REM Create virtual environment
if not exist "venv" (
    echo 📝 Creating Python virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ Failed to create virtual environment
        pause
        exit /b 1
    ) else (
        echo ✅ Virtual environment created
    )
) else (
    echo ✅ Virtual environment already exists
)

REM Activate virtual environment and install dependencies
echo 📦 Installing Python dependencies...
call venv\Scripts\activate
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ Failed to install Python dependencies
    pause
    exit /b 1
) else (
    echo ✅ Python dependencies installed
)

REM Test backend setup
echo 🧪 Testing backend setup...
python test_setup.py
if errorlevel 1 (
    echo ⚠️  Backend setup test had issues ^(check output above^)
) else (
    echo ✅ Backend setup test passed
)

cd ..

REM Test frontend setup
echo.
echo 🧪 Testing frontend setup...
node test_frontend.js
if errorlevel 1 (
    echo ⚠️  Frontend setup test had issues ^(check output above^)
) else (
    echo ✅ Frontend setup test passed
)

REM Final instructions
echo.
echo 🎉 Setup completed successfully!
echo.
echo 🚀 To start BrainBuddy:
echo    start.bat
echo.
echo 📱 Access points:
echo    • Electron App: Launches automatically
echo    • Web Interface: http://localhost:3000
echo    • Backend API: http://localhost:8000
echo    • API Docs: http://localhost:8000/docs
echo.
echo 📱 Optional Telegram Bot Setup:
echo    1. Create a bot with @BotFather on Telegram
echo    2. Add your bot token to .env file
echo    3. Restart the application
echo.
echo 🛠️ Manual start commands:
echo    Backend:  cd backend ^&^& venv\Scripts\activate ^&^& python main.py
echo    Frontend: cd frontend ^&^& npm start
echo    Electron: npm run electron:dev
echo.
echo 💡 Tip: Use 'ollama serve' if you need to restart Ollama
echo.

REM Ask if user wants to start now
set /p START_NOW="Would you like to start BrainBuddy now? (y/n): "
if /i "!START_NOW!"=="y" (
    echo 🚀 Starting BrainBuddy...
    start.bat
) else (
    echo.
    echo You can start BrainBuddy later by running: start.bat
)

pause
