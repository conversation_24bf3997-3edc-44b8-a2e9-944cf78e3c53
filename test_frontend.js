#!/usr/bin/env node
/**
 * Frontend setup test script
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🧠 BrainBuddy Frontend Setup Test');
console.log('=' .repeat(40));

function testFileExists(filePath, description) {
  const exists = fs.existsSync(filePath);
  console.log(`${exists ? '✅' : '❌'} ${description}: ${filePath}`);
  return exists;
}

function testDirectoryExists(dirPath, description) {
  const exists = fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory();
  console.log(`${exists ? '✅' : '❌'} ${description}: ${dirPath}`);
  return exists;
}

function testPackageJson() {
  console.log('\n🔍 Testing package.json files...');
  
  let allGood = true;
  
  // Root package.json
  if (testFileExists('package.json', 'Root package.json')) {
    try {
      const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      console.log(`   - Name: ${pkg.name}`);
      console.log(`   - Version: ${pkg.version}`);
      console.log(`   - Scripts: ${Object.keys(pkg.scripts || {}).join(', ')}`);
    } catch (e) {
      console.log('❌ Invalid JSON in root package.json');
      allGood = false;
    }
  } else {
    allGood = false;
  }
  
  // Frontend package.json
  if (testFileExists('frontend/package.json', 'Frontend package.json')) {
    try {
      const pkg = JSON.parse(fs.readFileSync('frontend/package.json', 'utf8'));
      console.log(`   - Name: ${pkg.name}`);
      console.log(`   - React version: ${pkg.dependencies?.react || 'Not found'}`);
      console.log(`   - Scripts: ${Object.keys(pkg.scripts || {}).join(', ')}`);
    } catch (e) {
      console.log('❌ Invalid JSON in frontend package.json');
      allGood = false;
    }
  } else {
    allGood = false;
  }
  
  return allGood;
}

function testFileStructure() {
  console.log('\n🔍 Testing file structure...');
  
  const requiredFiles = [
    'frontend/src/index.js',
    'frontend/src/App.js',
    'frontend/src/index.css',
    'frontend/src/App.css',
    'frontend/main.js',
    'frontend/public/index.html',
    'frontend/tailwind.config.js',
    'frontend/postcss.config.js'
  ];
  
  const requiredDirs = [
    'frontend/src',
    'frontend/src/components',
    'frontend/src/context',
    'frontend/src/services',
    'frontend/src/utils',
    'frontend/src/hooks',
    'frontend/public'
  ];
  
  let allFilesExist = true;
  let allDirsExist = true;
  
  console.log('\n📁 Required directories:');
  for (const dir of requiredDirs) {
    if (!testDirectoryExists(dir, 'Directory')) {
      allDirsExist = false;
    }
  }
  
  console.log('\n📄 Required files:');
  for (const file of requiredFiles) {
    if (!testFileExists(file, 'File')) {
      allFilesExist = false;
    }
  }
  
  return allFilesExist && allDirsExist;
}

function testComponents() {
  console.log('\n🔍 Testing React components...');
  
  const components = [
    'frontend/src/components/Sidebar.js',
    'frontend/src/components/TaskView.js',
    'frontend/src/components/TaskItem.js',
    'frontend/src/components/TaskForm.js',
    'frontend/src/components/ChatView.js',
    'frontend/src/components/HabitsView.js',
    'frontend/src/components/CalendarView.js'
  ];
  
  let allExist = true;
  
  for (const component of components) {
    if (!testFileExists(component, 'Component')) {
      allExist = false;
    }
  }
  
  return allExist;
}

function testServices() {
  console.log('\n🔍 Testing service files...');
  
  const services = [
    'frontend/src/services/api.js',
    'frontend/src/services/taskService.js',
    'frontend/src/services/aiService.js',
    'frontend/src/services/habitService.js'
  ];
  
  let allExist = true;
  
  for (const service of services) {
    if (!testFileExists(service, 'Service')) {
      allExist = false;
    }
  }
  
  return allExist;
}

function testContext() {
  console.log('\n🔍 Testing context providers...');
  
  const contexts = [
    'frontend/src/context/TaskContext.js',
    'frontend/src/context/AIContext.js'
  ];
  
  let allExist = true;
  
  for (const context of contexts) {
    if (!testFileExists(context, 'Context')) {
      allExist = false;
    }
  }
  
  return allExist;
}

function testUtils() {
  console.log('\n🔍 Testing utility files...');
  
  const utils = [
    'frontend/src/utils/constants.js',
    'frontend/src/utils/helpers.js'
  ];
  
  const hooks = [
    'frontend/src/hooks/useLocalStorage.js',
    'frontend/src/hooks/useKeyboard.js'
  ];
  
  let allExist = true;
  
  for (const util of utils) {
    if (!testFileExists(util, 'Utility')) {
      allExist = false;
    }
  }
  
  for (const hook of hooks) {
    if (!testFileExists(hook, 'Hook')) {
      allExist = false;
    }
  }
  
  return allExist;
}

function testNodeModules() {
  console.log('\n🔍 Testing dependencies...');
  
  // Check if node_modules exists
  const rootNodeModules = testDirectoryExists('node_modules', 'Root node_modules');
  const frontendNodeModules = testDirectoryExists('frontend/node_modules', 'Frontend node_modules');
  
  if (!rootNodeModules) {
    console.log('💡 Run: npm install');
  }
  
  if (!frontendNodeModules) {
    console.log('💡 Run: cd frontend && npm install');
  }
  
  return rootNodeModules && frontendNodeModules;
}

function testTailwindConfig() {
  console.log('\n🔍 Testing Tailwind configuration...');
  
  if (!testFileExists('frontend/tailwind.config.js', 'Tailwind config')) {
    return false;
  }
  
  try {
    const config = fs.readFileSync('frontend/tailwind.config.js', 'utf8');
    const hasContent = config.includes('content:');
    const hasTheme = config.includes('theme:');
    
    console.log(`   - Has content config: ${hasContent ? '✅' : '❌'}`);
    console.log(`   - Has theme config: ${hasTheme ? '✅' : '❌'}`);
    
    return hasContent && hasTheme;
  } catch (e) {
    console.log('❌ Error reading Tailwind config');
    return false;
  }
}

function testElectronConfig() {
  console.log('\n🔍 Testing Electron configuration...');
  
  if (!testFileExists('frontend/main.js', 'Electron main.js')) {
    return false;
  }
  
  try {
    const mainJs = fs.readFileSync('frontend/main.js', 'utf8');
    const hasElectron = mainJs.includes('electron');
    const hasBrowserWindow = mainJs.includes('BrowserWindow');
    
    console.log(`   - Imports Electron: ${hasElectron ? '✅' : '❌'}`);
    console.log(`   - Creates BrowserWindow: ${hasBrowserWindow ? '✅' : '❌'}`);
    
    return hasElectron && hasBrowserWindow;
  } catch (e) {
    console.log('❌ Error reading Electron main.js');
    return false;
  }
}

function runTests() {
  const tests = [
    { name: 'Package.json files', test: testPackageJson },
    { name: 'File structure', test: testFileStructure },
    { name: 'React components', test: testComponents },
    { name: 'Service files', test: testServices },
    { name: 'Context providers', test: testContext },
    { name: 'Utility files', test: testUtils },
    { name: 'Dependencies', test: testNodeModules },
    { name: 'Tailwind config', test: testTailwindConfig },
    { name: 'Electron config', test: testElectronConfig }
  ];
  
  const results = [];
  
  for (const { name, test } of tests) {
    console.log(`\n${'='.repeat(20)} ${name} ${'='.repeat(20)}`);
    try {
      const result = test();
      results.push({ name, passed: result });
    } catch (error) {
      console.log(`❌ ${name} test crashed: ${error.message}`);
      results.push({ name, passed: false });
    }
  }
  
  // Summary
  console.log(`\n${'='.repeat(20)} Summary ${'='.repeat(20)}`);
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  
  for (const { name, passed } of results) {
    console.log(`${passed ? '✅' : '❌'} ${name}`);
  }
  
  console.log(`\nTests passed: ${passed}/${total}`);
  
  if (passed === total) {
    console.log('\n🎉 All frontend tests passed!');
    console.log('\n🚀 Next steps:');
    console.log('1. Make sure backend is running');
    console.log('2. Start frontend: cd frontend && npm start');
    console.log('3. Start Electron: npm run electron:dev');
  } else {
    console.log(`\n⚠️  ${total - passed} test(s) failed. Please fix the issues above.`);
    
    if (!results.find(r => r.name === 'Dependencies')?.passed) {
      console.log('\n💡 Try running:');
      console.log('   npm install');
      console.log('   cd frontend && npm install');
    }
  }
  
  return passed === total;
}

// Run the tests
if (require.main === module) {
  const success = runTests();
  process.exit(success ? 0 : 1);
}

module.exports = { runTests };
