import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Sidebar from './components/Sidebar';
import TaskView from './components/TaskView';
import ChatView from './components/ChatView';
import HabitsView from './components/HabitsView';
import CalendarView from './components/CalendarView';
import { TaskProvider } from './context/TaskContext';
import { AIProvider } from './context/AIContext';
import './App.css';

function App() {
  const [currentView, setCurrentView] = useState('inbox');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  return (
    <TaskProvider>
      <AIProvider>
        <div className="flex h-screen bg-dark-900 text-slate-200">
          <Sidebar 
            currentView={currentView}
            setCurrentView={setCurrentView}
            collapsed={sidebarCollapsed}
            setCollapsed={setSidebarCollapsed}
          />
          
          <main className={`flex-1 flex transition-all duration-300 ${
            sidebarCollapsed ? 'ml-16' : 'ml-64'
          }`}>
            <div className="flex-1 overflow-hidden">
              {currentView === 'chat' ? (
                <ChatView />
              ) : currentView === 'habits' ? (
                <HabitsView />
              ) : currentView === 'calendar' ? (
                <CalendarView />
              ) : (
                <TaskView currentView={currentView} />
              )}
            </div>
          </main>
        </div>
      </AIProvider>
    </TaskProvider>
  );
}

export default App;
