import React, { useState } from 'react';
import { 
  Check, 
  Clock, 
  Calendar, 
  Flag, 
  MoreHorizontal, 
  Edit, 
  Trash2,
  ArrowRight
} from 'lucide-react';
import { format, isToday, isPast, isFuture } from 'date-fns';

const TaskItem = ({ task, onUpdate, onDelete, onClick }) => {
  const [showMenu, setShowMenu] = useState(false);
  const [isCompleting, setIsCompleting] = useState(false);

  const handleToggleComplete = async (e) => {
    e.stopPropagation();
    setIsCompleting(true);
    try {
      await onUpdate(task.id, {
        status: task.status === 'completed' ? 'pending' : 'completed',
        completed_at: task.status === 'completed' ? null : new Date().toISOString()
      });
    } finally {
      setIsCompleting(false);
    }
  };

  const handleDelete = (e) => {
    e.stopPropagation();
    if (window.confirm('Are you sure you want to delete this task?')) {
      onDelete(task.id);
    }
    setShowMenu(false);
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'text-red-400 bg-red-400/10';
      case 'medium': return 'text-yellow-400 bg-yellow-400/10';
      case 'low': return 'text-green-400 bg-green-400/10';
      default: return 'text-slate-400 bg-slate-400/10';
    }
  };

  const getDateInfo = () => {
    if (!task.due_date) return null;
    
    const dueDate = new Date(task.due_date);
    const isOverdue = isPast(dueDate) && !isToday(dueDate) && task.status !== 'completed';
    
    return {
      formatted: isToday(dueDate) ? 'Today' : format(dueDate, 'MMM d'),
      isOverdue,
      isToday: isToday(dueDate),
      isFuture: isFuture(dueDate)
    };
  };

  const dateInfo = getDateInfo();

  return (
    <div 
      className={`task-item relative ${task.status === 'completed' ? 'opacity-60' : ''}`}
      onClick={onClick}
    >
      <div className="flex items-start space-x-3">
        {/* Completion Checkbox */}
        <button
          onClick={handleToggleComplete}
          disabled={isCompleting}
          className={`flex-shrink-0 w-5 h-5 rounded border-2 flex items-center justify-center transition-all duration-200 ${
            task.status === 'completed'
              ? 'bg-primary-600 border-primary-600 text-white'
              : 'border-slate-500 hover:border-primary-500'
          } ${isCompleting ? 'opacity-50' : ''}`}
        >
          {task.status === 'completed' && <Check className="w-3 h-3" />}
        </button>

        {/* Task Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h3 className={`font-medium ${
                task.status === 'completed' 
                  ? 'line-through text-slate-400' 
                  : 'text-white'
              }`}>
                {task.title}
              </h3>
              
              {task.description && (
                <p className="text-slate-400 text-sm mt-1 line-clamp-2">
                  {task.description}
                </p>
              )}

              {/* Task Meta */}
              <div className="flex items-center space-x-4 mt-2">
                {/* Priority */}
                {task.priority && task.priority !== 'none' && (
                  <div className={`flex items-center space-x-1 px-2 py-1 rounded text-xs ${getPriorityColor(task.priority)}`}>
                    <Flag className="w-3 h-3" />
                    <span className="capitalize">{task.priority}</span>
                  </div>
                )}

                {/* Due Date */}
                {dateInfo && (
                  <div className={`flex items-center space-x-1 text-xs ${
                    dateInfo.isOverdue 
                      ? 'text-red-400' 
                      : dateInfo.isToday 
                        ? 'text-yellow-400' 
                        : 'text-slate-400'
                  }`}>
                    <Calendar className="w-3 h-3" />
                    <span>{dateInfo.formatted}</span>
                  </div>
                )}

                {/* Created Date */}
                <div className="flex items-center space-x-1 text-xs text-slate-500">
                  <Clock className="w-3 h-3" />
                  <span>{format(new Date(task.created_at), 'MMM d')}</span>
                </div>
              </div>
            </div>

            {/* Actions Menu */}
            <div className="relative">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setShowMenu(!showMenu);
                }}
                className="p-1 rounded hover:bg-slate-700 text-slate-400 hover:text-white transition-colors"
              >
                <MoreHorizontal className="w-4 h-4" />
              </button>

              {showMenu && (
                <div className="absolute right-0 top-8 bg-slate-800 border border-slate-600 rounded-lg shadow-lg z-10 min-w-32">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onClick();
                      setShowMenu(false);
                    }}
                    className="flex items-center space-x-2 w-full px-3 py-2 text-sm text-slate-300 hover:bg-slate-700 rounded-t-lg"
                  >
                    <Edit className="w-4 h-4" />
                    <span>Edit</span>
                  </button>
                  <button
                    onClick={handleDelete}
                    className="flex items-center space-x-2 w-full px-3 py-2 text-sm text-red-400 hover:bg-slate-700 rounded-b-lg"
                  >
                    <Trash2 className="w-4 h-4" />
                    <span>Delete</span>
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Overdue Indicator */}
      {dateInfo?.isOverdue && task.status !== 'completed' && (
        <div className="absolute left-0 top-0 bottom-0 w-1 bg-red-500 rounded-l-lg"></div>
      )}
    </div>
  );
};

export default TaskItem;
