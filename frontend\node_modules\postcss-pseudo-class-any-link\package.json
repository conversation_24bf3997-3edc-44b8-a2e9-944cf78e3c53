{"name": "postcss-pseudo-class-any-link", "description": "Use the :any-link pseudo-class in CSS", "version": "7.1.6", "author": "<PERSON> <<EMAIL>>", "license": "CC0-1.0", "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "engines": {"node": "^12 || ^14 || >=16"}, "main": "dist/index.cjs", "module": "dist/index.mjs", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs", "default": "./dist/index.mjs"}}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist"], "dependencies": {"postcss-selector-parser": "^6.0.10"}, "peerDependencies": {"postcss": "^8.2"}, "scripts": {"build": "rollup -c ../../rollup/default.js", "clean": "node -e \"fs.rmSync('./dist', { recursive: true, force: true });\"", "docs": "node ../../.github/bin/generate-docs/install.mjs", "lint": "npm run lint:eslint && npm run lint:package-json", "lint:eslint": "eslint ./src --ext .js --ext .ts --ext .mjs --no-error-on-unmatched-pattern", "lint:package-json": "node ../../.github/bin/format-package-json.mjs", "prepublishOnly": "npm run clean && npm run build && npm run test", "test": "node .tape.mjs && npm run test:exports", "test:exports": "node ./test/_import.mjs && node ./test/_require.cjs", "test:rewrite-expects": "REWRITE_EXPECTS=true node .tape.mjs"}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-pseudo-class-any-link#readme", "repository": {"type": "git", "url": "https://github.com/csstools/postcss-plugins.git", "directory": "plugins/postcss-pseudo-class-any-link"}, "bugs": "https://github.com/csstools/postcss-plugins/issues", "keywords": ["a", "any-link", "area", "css", "href", "hyperlink", "link", "postcss", "postcss-plugin", "visited"], "csstools": {"exportName": "postcssPseudoClassAnyLink", "humanReadableName": "PostCSS Pseudo Class Any Link"}, "volta": {"extends": "../../package.json"}}