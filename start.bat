@echo off
echo 🧠 Starting BrainBuddy AI Personal Assistant...

REM Check AI Provider configuration
echo 🤖 Checking AI Provider...
set AI_PROVIDER=ollama
for /f "tokens=2 delims==" %%a in ('findstr "^AI_PROVIDER=" .env 2^>nul') do set AI_PROVIDER=%%a
echo    Current provider: !AI_PROVIDER!

if "!AI_PROVIDER!"=="ollama" (
    echo 🔍 Checking Ollama...
    curl -s http://localhost:11434/api/tags >nul 2>&1
    if errorlevel 1 (
        echo ⚠️  Ollama is not running. Please start Ollama first:
        echo    ollama serve
        echo    ollama pull mistral
        echo.
        echo 💡 Alternative: Switch to OpenAI by editing .env:
        echo    AI_PROVIDER=openai
        echo    OPENAI_API_KEY=your_api_key_here
        pause
        exit /b 1
    )
    echo ✅ Ollama is running
) else if "!AI_PROVIDER!"=="openai" (
    echo 🔍 Checking OpenAI configuration...

    REM Check if API key is configured
    findstr "^OPENAI_API_KEY=your_openai_api_key_here" .env >nul 2>&1
    if not errorlevel 1 (
        echo ⚠️  OpenAI API key not configured!
        echo    Please set your API key in .env file:
        echo    OPENAI_API_KEY=sk-your-actual-api-key
        echo.
        echo 💡 Get an API key from: https://platform.openai.com/api-keys
        echo 💡 Alternative: Switch to Ollama ^(free, local^):
        echo    AI_PROVIDER=ollama
        pause
        exit /b 1
    )

    REM Check if API key exists at all
    findstr "^OPENAI_API_KEY=" .env >nul 2>&1
    if errorlevel 1 (
        echo ⚠️  OpenAI API key not found in .env file!
        echo    Please add your API key:
        echo    OPENAI_API_KEY=sk-your-actual-api-key
        pause
        exit /b 1
    )

    echo ✅ OpenAI API key configured
) else (
    echo ❌ Unknown AI provider: !AI_PROVIDER!
    echo    Supported providers: ollama, openai
    echo    Please check your .env file
    pause
    exit /b 1
)

REM Install frontend dependencies if needed
if not exist "node_modules" (
    echo 📦 Installing frontend dependencies...
    npm install
)

REM Create Python virtual environment if needed
if not exist "backend\venv" (
    echo 🐍 Creating Python virtual environment...
    cd backend
    python -m venv venv
    call venv\Scripts\activate
    pip install -r requirements.txt
    cd ..
)

REM Start backend
echo 🚀 Starting backend server...
cd backend
call venv\Scripts\activate
start "BrainBuddy Backend" python main.py
cd ..

REM Wait for backend to start
echo ⏳ Waiting for backend to start...
timeout /t 5 /nobreak >nul

REM Start frontend
echo 🎨 Starting frontend...
cd frontend
npm install
start "BrainBuddy Frontend" npm start
cd ..

echo ✅ BrainBuddy is starting up!
echo.
echo 🌐 Access Points:
echo    📱 Frontend: http://localhost:3000
echo    🔧 Backend API: http://localhost:8000
echo    📚 API Docs: http://localhost:8000/docs
echo.
echo 🤖 AI Provider: !AI_PROVIDER!
if "!AI_PROVIDER!"=="ollama" (
    for /f "tokens=2 delims==" %%a in ('findstr "^OLLAMA_MODEL=" .env 2^>nul') do set OLLAMA_MODEL=%%a
    if "!OLLAMA_MODEL!"=="" set OLLAMA_MODEL=mistral
    echo    🏠 Running locally with Ollama
    echo    📋 Model: !OLLAMA_MODEL!
) else if "!AI_PROVIDER!"=="openai" (
    for /f "tokens=2 delims==" %%a in ('findstr "^OPENAI_MODEL=" .env 2^>nul') do set OPENAI_MODEL=%%a
    for /f "tokens=2 delims==" %%a in ('findstr "^OPENAI_BASE_URL=" .env 2^>nul') do set OPENAI_BASE_URL=%%a
    if "!OPENAI_MODEL!"=="" set OPENAI_MODEL=gpt-3.5-turbo
    if "!OPENAI_BASE_URL!"=="" set OPENAI_BASE_URL=https://api.openai.com/v1
    echo    ☁️  Using OpenAI-compatible API
    echo    📋 Model: !OPENAI_MODEL!
    echo    🔗 Base URL: !OPENAI_BASE_URL!
)
echo.
echo 💡 Tips:
echo    • Use the chat feature to interact with AI
echo    • Try natural language: 'Remind me to call John tomorrow'
echo    • Check AI provider status: curl http://localhost:8000/ai/provider/status
echo.
echo Press any key to exit...
pause >nul
