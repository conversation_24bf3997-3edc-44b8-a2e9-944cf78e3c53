@echo off
echo 🧠 Starting BrainBuddy AI Personal Assistant...

REM Check if <PERSON><PERSON><PERSON> is running
echo 🔍 Checking Ollama...
curl -s http://localhost:11434/api/tags >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Ollama is not running. Please start Ollama first:
    echo    ollama serve
    echo    ollama pull mistral
    pause
    exit /b 1
)

echo ✅ Ollama is running

REM Install frontend dependencies if needed
if not exist "node_modules" (
    echo 📦 Installing frontend dependencies...
    npm install
)

REM Create Python virtual environment if needed
if not exist "backend\venv" (
    echo 🐍 Creating Python virtual environment...
    cd backend
    python -m venv venv
    call venv\Scripts\activate
    pip install -r requirements.txt
    cd ..
)

REM Start backend
echo 🚀 Starting backend server...
cd backend
call venv\Scripts\activate
start "BrainBuddy Backend" python main.py
cd ..

REM Wait for backend to start
echo ⏳ Waiting for backend to start...
timeout /t 5 /nobreak >nul

REM Start frontend
echo 🎨 Starting frontend...
cd frontend
npm install
start "BrainBuddy Frontend" npm start
cd ..

echo ✅ BrainBuddy is starting up!
echo 📱 Frontend: http://localhost:3000
echo 🔧 Backend API: http://localhost:8000
echo 📚 API Docs: http://localhost:8000/docs
echo.
echo Press any key to exit...
pause >nul
