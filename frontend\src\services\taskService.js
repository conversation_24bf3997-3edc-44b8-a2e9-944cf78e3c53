import api from './api';

export const taskService = {
  // Get all tasks
  async getTasks(filter = null) {
    const params = filter ? { filter } : {};
    return await api.get('/tasks', { params });
  },

  // Get task by ID
  async getTask(id) {
    return await api.get(`/tasks/${id}`);
  },

  // Create new task
  async createTask(taskData) {
    return await api.post('/tasks', taskData);
  },

  // Update task
  async updateTask(id, updates) {
    return await api.put(`/tasks/${id}`, updates);
  },

  // Delete task
  async deleteTask(id) {
    return await api.delete(`/tasks/${id}`);
  },

  // Get tasks by status
  async getTasksByStatus(status) {
    return await api.get('/tasks', { params: { status } });
  },

  // Get tasks for today
  async getTodayTasks() {
    return await api.get('/tasks/today');
  },

  // Get planned tasks
  async getPlannedTasks() {
    return await api.get('/tasks/planned');
  },

  // Get completed tasks
  async getCompletedTasks() {
    return await api.get('/tasks/completed');
  },

  // Move task to different status
  async moveTask(id, status) {
    return await api.patch(`/tasks/${id}/status`, { status });
  },

  // Bulk operations
  async bulkUpdateTasks(taskIds, updates) {
    return await api.patch('/tasks/bulk', { task_ids: taskIds, updates });
  },

  async bulkDeleteTasks(taskIds) {
    return await api.delete('/tasks/bulk', { data: { task_ids: taskIds } });
  },

  // Search tasks
  async searchTasks(query) {
    return await api.get('/tasks/search', { params: { q: query } });
  },

  // Get task statistics
  async getTaskStats() {
    return await api.get('/tasks/stats');
  },
};
