# BrainBuddy - AI Personal Assistant

A local AI-powered task management and personal assistant app with Telegram integration.

## 🌟 Features

- 📋 **Task Management**: Inbox, Today, Planned, Completed views
- 🤖 **AI-Powered**: Natural language processing with Ollama
- 📱 **Telegram Bot**: Remote task management via Telegram
- 🎯 **Habit Tracking**: Build better habits with AI feedback
- 📅 **Calendar**: Event management and scheduling
- 💬 **AI Chat**: Proactive suggestions and motivational feedback
- 📊 **Analytics**: Productivity insights and pattern analysis
- 🔄 **Smart Scheduling**: AI-optimized task scheduling
- 🌙 **Dark Theme**: Beautiful, modern interface

## 🛠️ Tech Stack

- **Frontend**: Electron + React + Tailwind CSS
- **Backend**: Python FastAPI
- **Database**: SQLite
- **AI**: Ollama (local LLM)
- **Bot**: Telegram Bot API

## 🚀 Quick Start

### Prerequisites

1. **Node.js 18+** - [Download here](https://nodejs.org/)
2. **Python 3.9+** - [Download here](https://python.org/)
3. **Ollama** - [Install Ollama](https://ollama.ai/)

### 🔧 Installation

1. **Clone the repository**:
```bash
git clone <repository-url>
cd BrainBuddy
```

2. **Install Ollama and pull the model**:
```bash
# Start Ollama service
ollama serve

# In another terminal, pull the model
ollama pull mistral
```

3. **Quick start with scripts**:

**On Linux/Mac**:
```bash
chmod +x start.sh
./start.sh
```

**On Windows**:
```bash
start.bat
```

### 🔧 Manual Setup

If you prefer manual setup:

1. **Install frontend dependencies**:
```bash
npm install
cd frontend
npm install
cd ..
```

2. **Setup Python backend**:
```bash
cd backend
python -m venv venv

# On Linux/Mac:
source venv/bin/activate

# On Windows:
venv\Scripts\activate

pip install -r requirements.txt
cd ..
```

3. **Start the backend**:
```bash
cd backend
source venv/bin/activate  # or venv\Scripts\activate on Windows
python main.py
```

4. **Start the frontend** (in another terminal):
```bash
cd frontend
npm start
```

5. **Start Electron app** (in another terminal):
```bash
npm run electron:dev
```

## 🌐 Access Points

- **Electron App**: Launches automatically
- **Web Interface**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

## 📱 Telegram Bot Setup (Optional)

1. **Create a Telegram bot**:
   - Message @BotFather on Telegram
   - Use `/newbot` command
   - Get your bot token

2. **Configure environment**:
```bash
# Copy example environment file
cp .env.example .env

# Edit .env and add your bot token:
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_CHAT_ID=your_chat_id_here
```

3. **Set webhook** (for production):
```bash
curl -X POST http://localhost:8000/telegram/set-webhook \
  -H "Content-Type: application/json" \
  -d '{"webhook_url": "https://your-domain.com/telegram/webhook"}'
```

## 🎯 Usage

### Task Management
- **Create tasks**: Use natural language like "Remind me to call John tomorrow"
- **Organize**: Drag tasks between Inbox, Today, Planned, Completed
- **AI Enhancement**: Let AI suggest priorities and due dates

### AI Chat
- Ask questions: "What should I focus on today?"
- Get insights: "How am I doing with my habits?"
- Natural task creation: "Buy groceries this weekend"

### Telegram Commands
- `/newtask <description>` - Create a new task
- `/list` - Show all pending tasks
- `/today` - Show today's tasks
- `/done <task_id>` - Mark task as completed
- `/habits` - Show your habits
- `/stats` - Show productivity statistics

### Habit Tracking
- Create daily, weekly, or custom habits
- Track streaks and completion rates
- Get AI-powered recommendations

## 📁 Project Structure

```
BrainBuddy/
├── frontend/                 # Electron + React app
│   ├── src/
│   │   ├── components/      # React components
│   │   ├── context/         # React context providers
│   │   ├── services/        # API services
│   │   └── ...
│   ├── main.js             # Electron main process
│   └── package.json
├── backend/                 # FastAPI server
│   ├── database/           # Database models and config
│   ├── routers/            # API route handlers
│   ├── services/           # Business logic services
│   ├── main.py             # FastAPI app entry point
│   └── requirements.txt
├── .env                    # Environment variables
├── start.sh               # Linux/Mac startup script
├── start.bat              # Windows startup script
└── README.md
```

## 🔧 Development

### Backend Development
```bash
cd backend
source venv/bin/activate
uvicorn main:app --reload --host localhost --port 8000
```

### Frontend Development
```bash
cd frontend
npm start  # React development server
npm run electron:dev  # Electron app
```

### Database
- SQLite database is created automatically
- Located at `backend/brainbuddy.db`
- View with any SQLite browser

## 🤖 AI Features

### Natural Language Processing
- **Task Creation**: "Remind me to call dentist tomorrow at 2pm"
- **Smart Parsing**: Extracts title, description, priority, due date
- **Context Aware**: Understands relative dates and times

### AI Chat Assistant
- **Productivity Coaching**: Get personalized advice
- **Task Suggestions**: AI recommends what to focus on
- **Motivational Feedback**: Encouraging messages based on progress
- **Weekly Planning**: AI-generated weekly plans

### Smart Scheduling
- **Optimal Timing**: AI suggests best times for tasks
- **Workload Balancing**: Distributes tasks evenly
- **Priority Optimization**: High-priority tasks get better slots

## 📊 Analytics & Insights

- **Completion Rates**: Track your productivity over time
- **Habit Streaks**: Monitor habit consistency
- **Pattern Analysis**: AI identifies productivity patterns
- **Bottleneck Detection**: Find what's slowing you down
- **Trend Visualization**: See your progress trends

## 🔒 Privacy & Security

- **100% Local**: All data stays on your device
- **No Cloud**: No external servers (except Telegram)
- **Open Source**: Full transparency
- **Encrypted**: Sensitive data is protected

## 🛠️ Troubleshooting

### Common Issues

**Ollama not responding**:
```bash
# Check if Ollama is running
curl http://localhost:11434/api/tags

# Start Ollama if not running
ollama serve
```

**Backend won't start**:
```bash
# Check Python version
python --version  # Should be 3.9+

# Reinstall dependencies
cd backend
pip install -r requirements.txt
```

**Frontend issues**:
```bash
# Clear node modules and reinstall
rm -rf node_modules frontend/node_modules
npm install
cd frontend && npm install
```

**Database issues**:
```bash
# Delete and recreate database
rm backend/brainbuddy.db
# Restart backend to recreate tables
```

### Logs
- **Backend logs**: Check terminal where `python main.py` is running
- **Frontend logs**: Check browser console (F12)
- **Electron logs**: Check Electron app console

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🙏 Acknowledgments

- **Ollama** for local AI capabilities
- **FastAPI** for the excellent Python framework
- **React** and **Electron** for the frontend
- **Tailwind CSS** for beautiful styling

## 📞 Support

- **Issues**: Create a GitHub issue
- **Discussions**: Use GitHub Discussions
- **Documentation**: Check the `/docs` folder

---

**Made with ❤️ for productivity enthusiasts**
