import { useEffect, useCallback } from 'react';

/**
 * Custom hook for handling keyboard shortcuts
 */
export function useKeyboard(shortcuts = {}) {
  const handleKeyDown = useCallback((event) => {
    const { key, ctrlKey, metaKey, shiftKey, altKey } = event;
    const modifierKey = ctrlKey || metaKey; // Support both Ctrl and Cmd
    
    // Create a key combination string
    const combination = [
      modifierKey && 'mod',
      shiftKey && 'shift',
      altKey && 'alt',
      key.toLowerCase()
    ].filter(Boolean).join('+');
    
    // Check if this combination has a handler
    const handler = shortcuts[combination];
    if (handler) {
      event.preventDefault();
      handler(event);
    }
  }, [shortcuts]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);
}

/**
 * Common keyboard shortcuts for the app
 */
export const useAppKeyboards = (handlers = {}) => {
  const shortcuts = {
    // Navigation
    'mod+1': () => handlers.navigateToInbox?.(),
    'mod+2': () => handlers.navigateToToday?.(),
    'mod+3': () => handlers.navigateToPlanned?.(),
    'mod+4': () => handlers.navigateToCompleted?.(),
    'mod+5': () => handlers.navigateToHabits?.(),
    'mod+6': () => handlers.navigateToChat?.(),
    
    // Actions
    'mod+n': () => handlers.createNewTask?.(),
    'mod+shift+n': () => handlers.createNewHabit?.(),
    'mod+k': () => handlers.openCommandPalette?.(),
    'mod+/': () => handlers.openHelp?.(),
    'mod+,': () => handlers.openSettings?.(),
    
    // Task actions
    'mod+enter': () => handlers.completeTask?.(),
    'mod+d': () => handlers.deleteTask?.(),
    'mod+e': () => handlers.editTask?.(),
    
    // Search
    'mod+f': () => handlers.focusSearch?.(),
    'escape': () => handlers.closeModal?.(),
    
    // Chat
    'mod+shift+c': () => handlers.clearChat?.(),
    'mod+shift+s': () => handlers.sendMessage?.(),
  };

  useKeyboard(shortcuts);
};

/**
 * Hook for handling escape key to close modals
 */
export function useEscapeKey(callback) {
  useEffect(() => {
    const handleEscape = (event) => {
      if (event.key === 'Escape') {
        callback();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [callback]);
}

/**
 * Hook for handling click outside to close components
 */
export function useClickOutside(ref, callback) {
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (ref.current && !ref.current.contains(event.target)) {
        callback();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [ref, callback]);
}
