from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, date
from pydantic import BaseModel

from database.database import get_db
from database.models import Habit, HabitCompletion, HabitFrequency
from services.habit_service import HabitService

router = APIRouter()

# Pydantic models
class HabitCreate(BaseModel):
    name: str
    description: Optional[str] = None
    frequency: HabitFrequency = HabitFrequency.DAILY
    target_count: int = 1

class HabitUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    frequency: Optional[HabitFrequency] = None
    target_count: Optional[int] = None
    is_active: Optional[bool] = None

class HabitResponse(BaseModel):
    id: int
    name: str
    description: Optional[str]
    frequency: HabitFrequency
    target_count: int
    current_streak: int
    longest_streak: int
    total_completions: int
    created_at: datetime
    updated_at: datetime
    is_active: bool
    completed_today: Optional[bool] = False

    class Config:
        from_attributes = True

class HabitCompletionResponse(BaseModel):
    id: int
    habit_id: int
    completed_at: datetime
    notes: Optional[str]

    class Config:
        from_attributes = True

@router.get("/", response_model=List[HabitResponse])
async def get_habits(
    active_only: bool = Query(True),
    db: Session = Depends(get_db)
):
    """Get all habits"""
    habit_service = HabitService(db)
    habits = habit_service.get_habits(active_only=active_only)
    
    # Add completed_today status
    habit_responses = []
    for habit in habits:
        habit_dict = habit.to_dict()
        habit_dict["completed_today"] = habit_service.is_completed_today(habit.id)
        habit_responses.append(HabitResponse(**habit_dict))
    
    return habit_responses

@router.get("/{habit_id}", response_model=HabitResponse)
async def get_habit(habit_id: int, db: Session = Depends(get_db)):
    """Get a specific habit"""
    habit_service = HabitService(db)
    habit = habit_service.get_habit(habit_id)
    if not habit:
        raise HTTPException(status_code=404, detail="Habit not found")
    
    habit_dict = habit.to_dict()
    habit_dict["completed_today"] = habit_service.is_completed_today(habit.id)
    return HabitResponse(**habit_dict)

@router.post("/", response_model=HabitResponse)
async def create_habit(habit_data: HabitCreate, db: Session = Depends(get_db)):
    """Create a new habit"""
    habit_service = HabitService(db)
    habit = habit_service.create_habit(habit_data.dict())
    
    habit_dict = habit.to_dict()
    habit_dict["completed_today"] = False
    return HabitResponse(**habit_dict)

@router.put("/{habit_id}", response_model=HabitResponse)
async def update_habit(
    habit_id: int,
    habit_data: HabitUpdate,
    db: Session = Depends(get_db)
):
    """Update a habit"""
    habit_service = HabitService(db)
    habit = habit_service.update_habit(habit_id, habit_data.dict(exclude_unset=True))
    if not habit:
        raise HTTPException(status_code=404, detail="Habit not found")
    
    habit_dict = habit.to_dict()
    habit_dict["completed_today"] = habit_service.is_completed_today(habit.id)
    return HabitResponse(**habit_dict)

@router.delete("/{habit_id}")
async def delete_habit(habit_id: int, db: Session = Depends(get_db)):
    """Delete a habit"""
    habit_service = HabitService(db)
    success = habit_service.delete_habit(habit_id)
    if not success:
        raise HTTPException(status_code=404, detail="Habit not found")
    return {"message": "Habit deleted successfully"}

@router.post("/{habit_id}/complete")
async def complete_habit(
    habit_id: int,
    notes: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Mark habit as completed for today"""
    habit_service = HabitService(db)
    
    # Check if already completed today
    if habit_service.is_completed_today(habit_id):
        raise HTTPException(status_code=400, detail="Habit already completed today")
    
    completion = habit_service.complete_habit(habit_id, notes)
    if not completion:
        raise HTTPException(status_code=404, detail="Habit not found")
    
    return {
        "message": "Habit completed successfully",
        "completion": HabitCompletionResponse.from_orm(completion)
    }

@router.get("/{habit_id}/progress")
async def get_habit_progress(
    habit_id: int,
    timeframe: str = Query("month", regex="^(week|month|year)$"),
    db: Session = Depends(get_db)
):
    """Get habit progress for a specific timeframe"""
    habit_service = HabitService(db)
    habit = habit_service.get_habit(habit_id)
    if not habit:
        raise HTTPException(status_code=404, detail="Habit not found")
    
    progress = habit_service.get_habit_progress(habit_id, timeframe)
    return progress

@router.get("/{habit_id}/stats")
async def get_habit_stats(habit_id: int, db: Session = Depends(get_db)):
    """Get detailed habit statistics"""
    habit_service = HabitService(db)
    habit = habit_service.get_habit(habit_id)
    if not habit:
        raise HTTPException(status_code=404, detail="Habit not found")
    
    stats = habit_service.get_habit_stats(habit_id)
    return stats

@router.get("/{habit_id}/history", response_model=List[HabitCompletionResponse])
async def get_habit_history(
    habit_id: int,
    limit: int = Query(30, le=100),
    db: Session = Depends(get_db)
):
    """Get habit completion history"""
    habit_service = HabitService(db)
    habit = habit_service.get_habit(habit_id)
    if not habit:
        raise HTTPException(status_code=404, detail="Habit not found")
    
    completions = habit_service.get_habit_history(habit_id, limit)
    return [HabitCompletionResponse.from_orm(completion) for completion in completions]

@router.post("/{habit_id}/reset-streak")
async def reset_habit_streak(habit_id: int, db: Session = Depends(get_db)):
    """Reset habit streak"""
    habit_service = HabitService(db)
    habit = habit_service.reset_habit_streak(habit_id)
    if not habit:
        raise HTTPException(status_code=404, detail="Habit not found")
    
    return {"message": "Habit streak reset successfully"}

@router.get("/progress/all")
async def get_all_habits_progress(
    timeframe: str = Query("week", regex="^(week|month|year)$"),
    db: Session = Depends(get_db)
):
    """Get progress for all habits"""
    habit_service = HabitService(db)
    progress = habit_service.get_all_habits_progress(timeframe)
    return progress
