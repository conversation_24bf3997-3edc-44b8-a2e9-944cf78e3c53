from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Dict, Any, List, Optional
from pydantic import BaseModel

from database.database import get_db
from services.ai_service import AIService
from services.task_service import TaskService
from services.habit_service import HabitService

router = APIRouter()

# Pydantic models
class ChatMessage(BaseModel):
    message: str
    context: Optional[Dict[str, Any]] = {}

class ProcessNLRequest(BaseModel):
    text: str
    context: Optional[Dict[str, Any]] = {}

class WeeklyPlanRequest(BaseModel):
    preferences: Optional[Dict[str, Any]] = {}

class MotivationRequest(BaseModel):
    context: Optional[Dict[str, Any]] = {}

class ScheduleTaskRequest(BaseModel):
    task: Dict[str, Any]
    preferences: Optional[Dict[str, Any]] = {}

class HabitRecommendationRequest(BaseModel):
    current_habits: List[Dict[str, Any]] = []

@router.post("/chat")
async def chat_with_ai(
    request: ChatMessage,
    db: Session = Depends(get_db)
):
    """Send a message to the AI assistant"""
    try:
        ai_service = AIService(db)
        response = await ai_service.process_chat_message(request.message, request.context)
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"AI processing failed: {str(e)}")

@router.post("/process")
async def process_natural_language(
    request: ProcessNLRequest,
    db: Session = Depends(get_db)
):
    """Process natural language input and extract structured data"""
    try:
        ai_service = AIService(db)
        result = await ai_service.process_natural_language(request.text, request.context)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"NL processing failed: {str(e)}")

@router.post("/suggestions")
async def get_ai_suggestions(
    context: Dict[str, Any] = {},
    db: Session = Depends(get_db)
):
    """Get AI suggestions based on current context"""
    try:
        ai_service = AIService(db)
        
        # Get current tasks and habits for context
        task_service = TaskService(db)
        habit_service = HabitService(db)
        
        recent_tasks = task_service.get_recent_tasks(days=7)
        today_tasks = task_service.get_today_tasks()
        habits = habit_service.get_habits()
        
        enhanced_context = {
            **context,
            "recent_tasks": [task.to_dict() for task in recent_tasks],
            "today_tasks": [task.to_dict() for task in today_tasks],
            "habits": [habit.to_dict() for habit in habits]
        }
        
        suggestions = await ai_service.get_suggestions(enhanced_context)
        return {"suggestions": suggestions}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get suggestions: {str(e)}")

@router.post("/weekly-plan")
async def generate_weekly_plan(
    request: WeeklyPlanRequest,
    db: Session = Depends(get_db)
):
    """Generate a weekly plan based on tasks and preferences"""
    try:
        ai_service = AIService(db)
        task_service = TaskService(db)
        
        # Get pending tasks
        pending_tasks = task_service.get_tasks(status="pending")
        
        context = {
            "pending_tasks": [task.to_dict() for task in pending_tasks],
            "preferences": request.preferences
        }
        
        plan = await ai_service.generate_weekly_plan(context)
        return plan
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate weekly plan: {str(e)}")

@router.post("/motivation")
async def get_motivational_feedback(
    request: MotivationRequest,
    db: Session = Depends(get_db)
):
    """Get motivational feedback based on user progress"""
    try:
        ai_service = AIService(db)
        task_service = TaskService(db)
        habit_service = HabitService(db)
        
        # Get user statistics
        task_stats = task_service.get_task_stats()
        habits_progress = habit_service.get_all_habits_progress("week")
        
        context = {
            **request.context,
            "task_stats": task_stats,
            "habits_progress": habits_progress
        }
        
        feedback = await ai_service.get_motivational_feedback(context)
        return feedback
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get motivation: {str(e)}")

@router.get("/analyze")
async def analyze_patterns(
    timeframe: str = "week",
    db: Session = Depends(get_db)
):
    """Analyze user patterns and provide insights"""
    try:
        ai_service = AIService(db)
        task_service = TaskService(db)
        habit_service = HabitService(db)
        
        # Get data for analysis
        if timeframe == "week":
            days = 7
        elif timeframe == "month":
            days = 30
        else:
            days = 7
        
        recent_tasks = task_service.get_recent_tasks(days=days)
        task_stats = task_service.get_task_stats()
        habits_progress = habit_service.get_all_habits_progress(timeframe)
        
        context = {
            "timeframe": timeframe,
            "recent_tasks": [task.to_dict() for task in recent_tasks],
            "task_stats": task_stats,
            "habits_progress": habits_progress
        }
        
        analysis = await ai_service.analyze_patterns(context)
        return analysis
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to analyze patterns: {str(e)}")

@router.get("/insights")
async def get_productivity_insights(db: Session = Depends(get_db)):
    """Get productivity insights and recommendations"""
    try:
        ai_service = AIService(db)
        task_service = TaskService(db)
        habit_service = HabitService(db)
        
        # Gather comprehensive data
        task_stats = task_service.get_task_stats()
        overdue_tasks = task_service.get_overdue_tasks()
        habits_progress = habit_service.get_all_habits_progress("month")
        recent_tasks = task_service.get_recent_tasks(days=30)
        
        context = {
            "task_stats": task_stats,
            "overdue_tasks": [task.to_dict() for task in overdue_tasks],
            "habits_progress": habits_progress,
            "recent_tasks": [task.to_dict() for task in recent_tasks]
        }
        
        insights = await ai_service.get_productivity_insights(context)
        return insights
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get insights: {str(e)}")

@router.post("/schedule")
async def smart_schedule_task(
    request: ScheduleTaskRequest,
    db: Session = Depends(get_db)
):
    """Use AI to intelligently schedule a task"""
    try:
        ai_service = AIService(db)
        task_service = TaskService(db)
        
        # Get existing tasks for context
        existing_tasks = task_service.get_tasks()
        
        context = {
            "new_task": request.task,
            "existing_tasks": [task.to_dict() for task in existing_tasks],
            "preferences": request.preferences
        }
        
        scheduled_task = await ai_service.smart_schedule_task(context)
        return scheduled_task
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to schedule task: {str(e)}")

@router.post("/habits/recommendations")
async def get_habit_recommendations(
    request: HabitRecommendationRequest,
    db: Session = Depends(get_db)
):
    """Get AI-powered habit recommendations"""
    try:
        ai_service = AIService(db)
        habit_service = HabitService(db)
        task_service = TaskService(db)
        
        # Get current habits and task patterns
        current_habits = habit_service.get_habits()
        task_stats = task_service.get_task_stats()
        recent_tasks = task_service.get_recent_tasks(days=30)
        
        context = {
            "current_habits": [habit.to_dict() for habit in current_habits],
            "task_patterns": [task.to_dict() for task in recent_tasks],
            "task_stats": task_stats
        }
        
        recommendations = await ai_service.get_habit_recommendations(context)
        return recommendations
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get habit recommendations: {str(e)}")

@router.get("/habits/{habit_id}/analysis")
async def analyze_habit_progress(
    habit_id: int,
    timeframe: str = "month",
    db: Session = Depends(get_db)
):
    """Analyze habit progress with AI insights"""
    try:
        ai_service = AIService(db)
        habit_service = HabitService(db)
        
        habit = habit_service.get_habit(habit_id)
        if not habit:
            raise HTTPException(status_code=404, detail="Habit not found")
        
        progress = habit_service.get_habit_progress(habit_id, timeframe)
        stats = habit_service.get_habit_stats(habit_id)
        
        context = {
            "habit": habit.to_dict(),
            "progress": progress,
            "stats": stats,
            "timeframe": timeframe
        }
        
        analysis = await ai_service.analyze_habit_progress(context)
        return analysis
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to analyze habit: {str(e)}")
