import React, { useState, useEffect } from 'react';
import { Plus, Target, TrendingUp, Calendar, Check, X } from 'lucide-react';
import { habitService } from '../services/habitService';
import { format, startOfWeek, endOfWeek, eachDayOfInterval, isToday } from 'date-fns';
import toast from 'react-hot-toast';

const HabitsView = () => {
  const [habits, setHabits] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [newHabit, setNewHabit] = useState({
    name: '',
    description: '',
    frequency: 'daily',
    target_count: 1,
  });

  useEffect(() => {
    loadHabits();
  }, []);

  const loadHabits = async () => {
    try {
      setLoading(true);
      const data = await habitService.getHabits();
      setHabits(data);
    } catch (error) {
      toast.error('Failed to load habits');
      console.error('Error loading habits:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateHabit = async (e) => {
    e.preventDefault();
    if (!newHabit.name.trim()) return;

    try {
      const habit = await habitService.createHabit(newHabit);
      setHabits([...habits, habit]);
      setNewHabit({ name: '', description: '', frequency: 'daily', target_count: 1 });
      setShowForm(false);
      toast.success('Habit created successfully');
    } catch (error) {
      toast.error('Failed to create habit');
      console.error('Error creating habit:', error);
    }
  };

  const handleCompleteHabit = async (habitId) => {
    try {
      await habitService.completeHabit(habitId);
      await loadHabits(); // Reload to get updated data
      toast.success('Habit completed!');
    } catch (error) {
      toast.error('Failed to complete habit');
      console.error('Error completing habit:', error);
    }
  };

  const getWeekDays = () => {
    const start = startOfWeek(new Date(), { weekStartsOn: 1 }); // Monday
    const end = endOfWeek(new Date(), { weekStartsOn: 1 });
    return eachDayOfInterval({ start, end });
  };

  const weekDays = getWeekDays();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-slate-700">
        <div>
          <h1 className="text-2xl font-bold text-white">Habits</h1>
          <p className="text-slate-400 mt-1">
            Build better habits, one day at a time
          </p>
        </div>
        
        <button
          onClick={() => setShowForm(true)}
          className="btn-primary flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>New Habit</span>
        </button>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-6">
        {habits.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-64 text-slate-400">
            <Target className="w-16 h-16 mb-4 text-slate-600" />
            <h3 className="text-lg font-medium mb-2">No habits yet</h3>
            <p className="text-center">
              Start building better habits by creating your first one!
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Week Overview */}
            <div className="bg-slate-800 rounded-lg p-6">
              <h2 className="text-lg font-semibold text-white mb-4">This Week</h2>
              <div className="grid grid-cols-7 gap-2 mb-4">
                {weekDays.map((day) => (
                  <div
                    key={day.toISOString()}
                    className={`text-center p-2 rounded ${
                      isToday(day) ? 'bg-primary-600 text-white' : 'text-slate-400'
                    }`}
                  >
                    <div className="text-xs font-medium">
                      {format(day, 'EEE')}
                    </div>
                    <div className="text-sm">
                      {format(day, 'd')}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Habits List */}
            <div className="space-y-4">
              {habits.map((habit) => (
                <HabitCard
                  key={habit.id}
                  habit={habit}
                  onComplete={() => handleCompleteHabit(habit.id)}
                  weekDays={weekDays}
                />
              ))}
            </div>
          </div>
        )}
      </div>

      {/* New Habit Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-slate-800 rounded-lg border border-slate-700 w-full max-w-md">
            <div className="flex items-center justify-between p-6 border-b border-slate-700">
              <h2 className="text-lg font-semibold text-white">New Habit</h2>
              <button
                onClick={() => setShowForm(false)}
                className="p-2 rounded-lg hover:bg-slate-700 text-slate-400 hover:text-white transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <form onSubmit={handleCreateHabit} className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Habit Name *
                </label>
                <input
                  type="text"
                  value={newHabit.name}
                  onChange={(e) => setNewHabit({ ...newHabit, name: e.target.value })}
                  placeholder="e.g., Drink 8 glasses of water"
                  className="input-field"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Description
                </label>
                <textarea
                  value={newHabit.description}
                  onChange={(e) => setNewHabit({ ...newHabit, description: e.target.value })}
                  placeholder="Why is this habit important to you?"
                  rows={3}
                  className="input-field resize-none"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Frequency
                </label>
                <select
                  value={newHabit.frequency}
                  onChange={(e) => setNewHabit({ ...newHabit, frequency: e.target.value })}
                  className="input-field"
                >
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="custom">Custom</option>
                </select>
              </div>

              <div className="flex space-x-3 pt-4">
                <button type="submit" className="btn-primary flex-1">
                  Create Habit
                </button>
                <button
                  type="button"
                  onClick={() => setShowForm(false)}
                  className="btn-secondary"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

const HabitCard = ({ habit, onComplete, weekDays }) => {
  const todayCompleted = habit.completed_today || false;
  const currentStreak = habit.current_streak || 0;

  return (
    <div className="bg-slate-800 rounded-lg p-6 border border-slate-700">
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-white">{habit.name}</h3>
          {habit.description && (
            <p className="text-slate-400 text-sm mt-1">{habit.description}</p>
          )}
        </div>
        
        <button
          onClick={onComplete}
          disabled={todayCompleted}
          className={`p-2 rounded-lg transition-colors ${
            todayCompleted
              ? 'bg-green-600 text-white cursor-not-allowed'
              : 'bg-slate-700 hover:bg-primary-600 text-slate-300 hover:text-white'
          }`}
        >
          <Check className="w-5 h-5" />
        </button>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary-400">{currentStreak}</div>
            <div className="text-xs text-slate-400">Day Streak</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-slate-300">{habit.frequency}</div>
            <div className="text-xs text-slate-400">Frequency</div>
          </div>
        </div>

        {/* Week Progress */}
        <div className="flex space-x-1">
          {weekDays.map((day, index) => {
            const dayCompleted = Math.random() > 0.5; // Mock data - replace with real completion data
            return (
              <div
                key={index}
                className={`w-6 h-6 rounded-full flex items-center justify-center text-xs ${
                  dayCompleted
                    ? 'bg-green-600 text-white'
                    : isToday(day)
                      ? 'bg-slate-600 border-2 border-primary-500'
                      : 'bg-slate-700'
                }`}
              >
                {dayCompleted && <Check className="w-3 h-3" />}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default HabitsView;
