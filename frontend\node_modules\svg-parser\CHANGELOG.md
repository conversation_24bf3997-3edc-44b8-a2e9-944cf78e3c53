# svg-parser changelog

## 2.0.3

* Fix reported error location ([#9](https://github.com/<PERSON>-<PERSON>/svg-parser/issues/9))

## 2.0.2

* Allow underscores in attribute names ([#4](https://github.com/<PERSON>-Harris/svg-parser/issues/4))

## 2.0.1

* Fix empty/space attributes

## 2.0.0

* Migrate to HAST

## 1.0.6

* Remove unused dependency

## 1.0.5

* Handle doctype and CDATA

## 1.0.4

* Handle unexpected end of input

## 1.0.3

* Prevent infinite loops on bad final closing tag

## 1.0.2

* Prevent `""=true` attributes

## 1.0.1

* Allow attributes with numbers (e.g. `x1`)
* Fix error messages

## 1.0.0

* First release
