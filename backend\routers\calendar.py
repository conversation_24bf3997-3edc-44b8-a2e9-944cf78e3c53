from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, date
from pydantic import BaseModel

from database.database import get_db
from database.models import Event
from services.calendar_service import CalendarService

router = APIRouter()

# Pydantic models
class EventCreate(BaseModel):
    title: str
    description: Optional[str] = None
    start_time: datetime
    end_time: Optional[datetime] = None
    location: Optional[str] = None
    event_type: str = "event"

class EventUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    location: Optional[str] = None
    event_type: Optional[str] = None

class EventResponse(BaseModel):
    id: int
    title: str
    description: Optional[str]
    start_time: datetime
    end_time: Optional[datetime]
    location: Optional[str]
    event_type: str
    created_at: datetime
    updated_at: datetime
    ai_generated: bool

    class Config:
        from_attributes = True

@router.get("/events", response_model=List[EventResponse])
async def get_events(
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    event_type: Optional[str] = None,
    limit: int = Query(100, le=500),
    db: Session = Depends(get_db)
):
    """Get events with optional filtering"""
    calendar_service = CalendarService(db)
    events = calendar_service.get_events(
        start_date=start_date,
        end_date=end_date,
        event_type=event_type,
        limit=limit
    )
    return [EventResponse.from_orm(event) for event in events]

@router.get("/events/today", response_model=List[EventResponse])
async def get_today_events(db: Session = Depends(get_db)):
    """Get today's events"""
    calendar_service = CalendarService(db)
    events = calendar_service.get_today_events()
    return [EventResponse.from_orm(event) for event in events]

@router.get("/events/upcoming", response_model=List[EventResponse])
async def get_upcoming_events(
    days: int = Query(7, ge=1, le=30),
    db: Session = Depends(get_db)
):
    """Get upcoming events"""
    calendar_service = CalendarService(db)
    events = calendar_service.get_upcoming_events(days=days)
    return [EventResponse.from_orm(event) for event in events]

@router.get("/events/{event_id}", response_model=EventResponse)
async def get_event(event_id: int, db: Session = Depends(get_db)):
    """Get a specific event"""
    calendar_service = CalendarService(db)
    event = calendar_service.get_event(event_id)
    if not event:
        raise HTTPException(status_code=404, detail="Event not found")
    return EventResponse.from_orm(event)

@router.post("/events", response_model=EventResponse)
async def create_event(event_data: EventCreate, db: Session = Depends(get_db)):
    """Create a new event"""
    calendar_service = CalendarService(db)
    event = calendar_service.create_event(event_data.dict())
    return EventResponse.from_orm(event)

@router.put("/events/{event_id}", response_model=EventResponse)
async def update_event(
    event_id: int,
    event_data: EventUpdate,
    db: Session = Depends(get_db)
):
    """Update an event"""
    calendar_service = CalendarService(db)
    event = calendar_service.update_event(event_id, event_data.dict(exclude_unset=True))
    if not event:
        raise HTTPException(status_code=404, detail="Event not found")
    return EventResponse.from_orm(event)

@router.delete("/events/{event_id}")
async def delete_event(event_id: int, db: Session = Depends(get_db)):
    """Delete an event"""
    calendar_service = CalendarService(db)
    success = calendar_service.delete_event(event_id)
    if not success:
        raise HTTPException(status_code=404, detail="Event not found")
    return {"message": "Event deleted successfully"}

@router.get("/calendar/{year}/{month}")
async def get_calendar_month(
    year: int,
    month: int,
    db: Session = Depends(get_db)
):
    """Get calendar view for a specific month"""
    calendar_service = CalendarService(db)
    calendar_data = calendar_service.get_calendar_month(year, month)
    return calendar_data

@router.get("/availability")
async def check_availability(
    start_time: datetime,
    end_time: datetime,
    db: Session = Depends(get_db)
):
    """Check availability for a time slot"""
    calendar_service = CalendarService(db)
    is_available = calendar_service.check_availability(start_time, end_time)
    return {
        "available": is_available,
        "start_time": start_time,
        "end_time": end_time
    }

@router.get("/free-slots")
async def get_free_slots(
    date: date,
    duration_minutes: int = Query(60, ge=15, le=480),
    db: Session = Depends(get_db)
):
    """Get available time slots for a specific date"""
    calendar_service = CalendarService(db)
    slots = calendar_service.get_free_slots(date, duration_minutes)
    return {"date": date, "duration_minutes": duration_minutes, "free_slots": slots}
