import React, { useState, useEffect } from 'react';
import { X, Calendar, Flag, Clock, Sparkles } from 'lucide-react';
import { format } from 'date-fns';
import { useAI } from '../context/AIContext';

const TaskForm = ({ task, onSubmit, onCancel, isEditing = false, defaultStatus = 'inbox' }) => {
  const { processNaturalLanguage, isThinking } = useAI();
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    priority: 'medium',
    due_date: '',
    status: 'pending',
    ...task
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [useAI, setUseAI] = useState(false);

  useEffect(() => {
    if (task) {
      setFormData({
        ...task,
        due_date: task.due_date ? format(new Date(task.due_date), 'yyyy-MM-dd') : ''
      });
    }
  }, [task]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!formData.title.trim()) return;

    setIsSubmitting(true);
    try {
      let processedData = { ...formData };

      // Process with AI if enabled
      if (useAI && formData.title) {
        try {
          const aiResult = await processNaturalLanguage(formData.title, {
            type: 'task_creation',
            current_data: formData
          });
          
          if (aiResult.suggestions) {
            processedData = {
              ...processedData,
              ...aiResult.suggestions,
              title: aiResult.suggestions.title || formData.title,
              description: aiResult.suggestions.description || formData.description,
              priority: aiResult.suggestions.priority || formData.priority,
              due_date: aiResult.suggestions.due_date || formData.due_date,
            };
          }
        } catch (aiError) {
          console.error('AI processing failed:', aiError);
          // Continue with original data if AI fails
        }
      }

      // Convert due_date to ISO string if provided
      if (processedData.due_date) {
        processedData.due_date = new Date(processedData.due_date).toISOString();
      }

      await onSubmit(processedData);
    } catch (error) {
      console.error('Failed to submit task:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-slate-800 rounded-lg border border-slate-700 w-full max-w-md max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-slate-700">
          <h2 className="text-lg font-semibold text-white">
            {isEditing ? 'Edit Task' : 'New Task'}
          </h2>
          <button
            onClick={onCancel}
            className="p-2 rounded-lg hover:bg-slate-700 text-slate-400 hover:text-white transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* AI Toggle */}
          {!isEditing && (
            <div className="flex items-center space-x-2 p-3 bg-slate-700/50 rounded-lg">
              <input
                type="checkbox"
                id="useAI"
                checked={useAI}
                onChange={(e) => setUseAI(e.target.checked)}
                className="rounded border-slate-600 bg-slate-700 text-primary-600 focus:ring-primary-500"
              />
              <label htmlFor="useAI" className="flex items-center space-x-2 text-sm text-slate-300">
                <Sparkles className="w-4 h-4 text-primary-400" />
                <span>Use AI to enhance task details</span>
              </label>
            </div>
          )}

          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              Task Title *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => handleChange('title', e.target.value)}
              placeholder={useAI ? "e.g., 'Call dentist tomorrow morning'" : "Enter task title"}
              className="input-field"
              required
            />
            {useAI && (
              <p className="text-xs text-slate-400 mt-1">
                💡 Try natural language like "Buy groceries this weekend" or "Meeting with John at 3pm"
              </p>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              placeholder="Add more details..."
              rows={3}
              className="input-field resize-none"
            />
          </div>

          {/* Priority */}
          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              <Flag className="w-4 h-4 inline mr-1" />
              Priority
            </label>
            <select
              value={formData.priority}
              onChange={(e) => handleChange('priority', e.target.value)}
              className="input-field"
            >
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
            </select>
          </div>

          {/* Due Date */}
          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">
              <Calendar className="w-4 h-4 inline mr-1" />
              Due Date
            </label>
            <input
              type="date"
              value={formData.due_date}
              onChange={(e) => handleChange('due_date', e.target.value)}
              className="input-field"
            />
          </div>

          {/* Status (for editing) */}
          {isEditing && (
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Status
              </label>
              <select
                value={formData.status}
                onChange={(e) => handleChange('status', e.target.value)}
                className="input-field"
              >
                <option value="pending">Pending</option>
                <option value="completed">Completed</option>
              </select>
            </div>
          )}

          {/* Actions */}
          <div className="flex space-x-3 pt-4">
            <button
              type="submit"
              disabled={isSubmitting || isThinking || !formData.title.trim()}
              className="btn-primary flex-1 flex items-center justify-center space-x-2"
            >
              {(isSubmitting || isThinking) ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>{isThinking ? 'AI Processing...' : 'Saving...'}</span>
                </>
              ) : (
                <span>{isEditing ? 'Update Task' : 'Create Task'}</span>
              )}
            </button>
            <button
              type="button"
              onClick={onCancel}
              className="btn-secondary"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default TaskForm;
