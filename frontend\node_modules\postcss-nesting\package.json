{"name": "postcss-nesting", "description": "Nest rules inside each other in CSS", "version": "10.2.0", "author": "<PERSON> <<EMAIL>>", "license": "CC0-1.0", "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "engines": {"node": "^12 || ^14 || >=16"}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "jsdelivr": "dist/index.mjs", "unpkg": "dist/index.mjs", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs", "default": "./dist/index.mjs"}}, "files": ["dist", "mod.js"], "dependencies": {"@csstools/selector-specificity": "^2.0.0", "postcss-selector-parser": "^6.0.10"}, "peerDependencies": {"postcss": "^8.2"}, "scripts": {"build": "rollup -c ../../rollup/default.js", "clean": "node -e \"fs.rmSync('./dist', { recursive: true, force: true });\"", "docs": "node ../../.github/bin/generate-docs/install.mjs", "lint": "npm run lint:eslint && npm run lint:package-json", "lint:eslint": "eslint ./src --ext .js --ext .ts --ext .mjs --no-error-on-unmatched-pattern", "lint:package-json": "node ../../.github/bin/format-package-json.mjs", "prepublishOnly": "npm run clean && npm run build && npm run test", "test": "node .tape.mjs && npm run test:exports", "test:deno": "deno run --unstable --allow-env --allow-read test/deno/test.js", "test:exports": "node ./test/_import.mjs && node ./test/_require.cjs", "test:rewrite-expects": "REWRITE_EXPECTS=true node .tape.mjs"}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-nesting#readme", "repository": {"type": "git", "url": "https://github.com/csstools/postcss-plugins.git", "directory": "plugins/postcss-nesting"}, "bugs": "https://github.com/csstools/postcss-plugins/issues", "keywords": ["at<PERSON>les", "child", "children", "css", "cssnext", "csswg", "nested", "nestings", "postcss", "postcss-plugin", "rules", "selectors", "specifications", "specs", "syntax", "w3c"], "csstools": {"exportName": "postcssNesting", "humanReadableName": "PostCSS Nesting"}, "volta": {"extends": "../../package.json"}}