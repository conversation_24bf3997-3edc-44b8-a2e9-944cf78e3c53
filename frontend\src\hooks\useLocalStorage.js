import { useState, useEffect } from 'react';

/**
 * Custom hook for managing localStorage with React state
 */
export function useLocalStorage(key, initialValue) {
  // Get value from localStorage or use initial value
  const [storedValue, setStoredValue] = useState(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  // Return a wrapped version of useState's setter function that persists the new value to localStorage
  const setValue = (value) => {
    try {
      // Allow value to be a function so we have the same API as useState
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      
      // Save state
      setStoredValue(valueToStore);
      
      // Save to localStorage
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue];
}

/**
 * Custom hook for managing user preferences
 */
export function usePreferences() {
  const [preferences, setPreferences] = useLocalStorage('brainbuddy_preferences', {
    theme: 'dark',
    sidebarCollapsed: false,
    notifications: true,
    aiSuggestions: true,
    autoSave: true,
    language: 'en',
    dateFormat: 'MMM d, yyyy',
    timeFormat: '24h',
    weekStartsOn: 1, // Monday
    defaultTaskPriority: 'medium',
    defaultHabitFrequency: 'daily'
  });

  const updatePreference = (key, value) => {
    setPreferences(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const resetPreferences = () => {
    setPreferences({
      theme: 'dark',
      sidebarCollapsed: false,
      notifications: true,
      aiSuggestions: true,
      autoSave: true,
      language: 'en',
      dateFormat: 'MMM d, yyyy',
      timeFormat: '24h',
      weekStartsOn: 1,
      defaultTaskPriority: 'medium',
      defaultHabitFrequency: 'daily'
    });
  };

  return {
    preferences,
    updatePreference,
    resetPreferences
  };
}
