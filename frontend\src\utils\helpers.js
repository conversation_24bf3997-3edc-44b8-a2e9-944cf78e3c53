import { format, isToday, isTomorrow, isYesterday, formatDistanceToNow } from 'date-fns';
import { TASK_PRIORITY, PRIORITY_COLORS, DATE_FORMATS } from './constants';

/**
 * Format a date for display
 */
export const formatDate = (date, formatStr = DATE_FORMATS.DISPLAY) => {
  if (!date) return '';
  return format(new Date(date), formatStr);
};

/**
 * Format a date relative to now (e.g., "Today", "Tomorrow", "2 days ago")
 */
export const formatRelativeDate = (date) => {
  if (!date) return '';
  
  const dateObj = new Date(date);
  
  if (isToday(dateObj)) return 'Today';
  if (isTomorrow(dateObj)) return 'Tomorrow';
  if (isYesterday(dateObj)) return 'Yesterday';
  
  return formatDistanceToNow(dateObj, { addSuffix: true });
};

/**
 * Get priority color classes
 */
export const getPriorityColors = (priority) => {
  return PRIORITY_COLORS[priority] || PRIORITY_COLORS[TASK_PRIORITY.MEDIUM];
};

/**
 * Get priority emoji
 */
export const getPriorityEmoji = (priority) => {
  switch (priority) {
    case TASK_PRIORITY.HIGH: return '🔴';
    case TASK_PRIORITY.MEDIUM: return '🟡';
    case TASK_PRIORITY.LOW: return '🟢';
    default: return '⚪';
  }
};

/**
 * Truncate text to specified length
 */
export const truncateText = (text, maxLength = 100) => {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

/**
 * Debounce function
 */
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * Generate a random ID
 */
export const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

/**
 * Check if a date is overdue
 */
export const isOverdue = (date) => {
  if (!date) return false;
  const now = new Date();
  const dueDate = new Date(date);
  return dueDate < now && !isToday(dueDate);
};

/**
 * Get task status color
 */
export const getTaskStatusColor = (status) => {
  switch (status) {
    case 'completed': return 'text-green-400';
    case 'pending': return 'text-yellow-400';
    case 'cancelled': return 'text-red-400';
    default: return 'text-slate-400';
  }
};

/**
 * Calculate completion percentage
 */
export const calculateCompletionRate = (completed, total) => {
  if (total === 0) return 0;
  return Math.round((completed / total) * 100);
};

/**
 * Format file size
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Validate email format
 */
export const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Get greeting based on time of day
 */
export const getGreeting = () => {
  const hour = new Date().getHours();
  if (hour < 12) return 'Good morning';
  if (hour < 17) return 'Good afternoon';
  return 'Good evening';
};

/**
 * Sort tasks by priority and due date
 */
export const sortTasks = (tasks) => {
  const priorityOrder = { high: 3, medium: 2, low: 1 };
  
  return [...tasks].sort((a, b) => {
    // First sort by priority
    const priorityDiff = (priorityOrder[b.priority] || 0) - (priorityOrder[a.priority] || 0);
    if (priorityDiff !== 0) return priorityDiff;
    
    // Then by due date (earlier dates first)
    if (a.due_date && b.due_date) {
      return new Date(a.due_date) - new Date(b.due_date);
    }
    if (a.due_date) return -1;
    if (b.due_date) return 1;
    
    // Finally by creation date (newer first)
    return new Date(b.created_at) - new Date(a.created_at);
  });
};

/**
 * Group tasks by date
 */
export const groupTasksByDate = (tasks) => {
  const groups = {};
  
  tasks.forEach(task => {
    let groupKey;
    if (task.due_date) {
      const dueDate = new Date(task.due_date);
      if (isToday(dueDate)) {
        groupKey = 'Today';
      } else if (isTomorrow(dueDate)) {
        groupKey = 'Tomorrow';
      } else if (isYesterday(dueDate)) {
        groupKey = 'Yesterday';
      } else {
        groupKey = format(dueDate, 'EEEE, MMM d');
      }
    } else {
      groupKey = 'No due date';
    }
    
    if (!groups[groupKey]) {
      groups[groupKey] = [];
    }
    groups[groupKey].push(task);
  });
  
  return groups;
};

/**
 * Calculate streak from completion dates
 */
export const calculateStreak = (completionDates) => {
  if (!completionDates || completionDates.length === 0) return 0;
  
  const sortedDates = completionDates
    .map(date => new Date(date).toDateString())
    .sort((a, b) => new Date(b) - new Date(a));
  
  let streak = 0;
  let currentDate = new Date();
  
  for (const dateStr of sortedDates) {
    const date = new Date(dateStr);
    const daysDiff = Math.floor((currentDate - date) / (1000 * 60 * 60 * 24));
    
    if (daysDiff === streak) {
      streak++;
      currentDate = date;
    } else {
      break;
    }
  }
  
  return streak;
};

/**
 * Local storage helpers
 */
export const storage = {
  get: (key, defaultValue = null) => {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch {
      return defaultValue;
    }
  },
  
  set: (key, value) => {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('Failed to save to localStorage:', error);
    }
  },
  
  remove: (key) => {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('Failed to remove from localStorage:', error);
    }
  }
};

/**
 * Copy text to clipboard
 */
export const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch {
    // Fallback for older browsers
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    try {
      document.execCommand('copy');
      return true;
    } catch {
      return false;
    } finally {
      document.body.removeChild(textArea);
    }
  }
};
