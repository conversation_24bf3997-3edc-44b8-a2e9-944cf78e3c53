# unquote

Remove wrapping quotes from a string. Returns an empty string if the first arg is falsey.

## Installation

```
npm install unquote
```


## Usage

Example
```js
var unquote = require('unquote')

unquote('"hello, world"') // 'hello, world'
unquote('\'hello, world\'') // 'hello, world'
```


## Running Tests

```
npm test
```

## License

([The MIT License](LICENSE))

Copyright 2017 <PERSON>
