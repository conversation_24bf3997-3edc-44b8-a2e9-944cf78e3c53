#!/usr/bin/env python3
"""
Test script to verify BrainBuddy backend setup
"""

import os
import sys
import asyncio
import httpx
from datetime import datetime

def test_imports():
    """Test if all required modules can be imported"""
    print("🔍 Testing imports...")
    
    try:
        import fastapi
        print("✅ FastAPI imported successfully")
    except ImportError as e:
        print(f"❌ FastAPI import failed: {e}")
        return False
    
    try:
        import sqlalchemy
        print("✅ SQLAlchemy imported successfully")
    except ImportError as e:
        print(f"❌ SQLAlchemy import failed: {e}")
        return False
    
    try:
        import httpx
        print("✅ HTTPX imported successfully")
    except ImportError as e:
        print(f"❌ HTTPX import failed: {e}")
        return False
    
    try:
        from database.database import init_db
        print("✅ Database module imported successfully")
    except ImportError as e:
        print(f"❌ Database module import failed: {e}")
        return False
    
    return True

def test_environment():
    """Test environment variables"""
    print("\n🔍 Testing environment...")
    
    required_vars = ["DATABASE_URL", "OLLAMA_BASE_URL", "OLLAMA_MODEL"]
    optional_vars = ["TELEGRAM_BOT_TOKEN", "TELEGRAM_CHAT_ID"]
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {value}")
        else:
            print(f"❌ {var}: Not set")
    
    for var in optional_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: Set")
        else:
            print(f"⚠️  {var}: Not set (optional)")

async def test_ollama():
    """Test Ollama connection"""
    print("\n🔍 Testing Ollama connection...")
    
    ollama_url = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(f"{ollama_url}/api/tags")
            if response.status_code == 200:
                data = response.json()
                models = data.get("models", [])
                print(f"✅ Ollama connected successfully")
                print(f"📋 Available models: {len(models)}")
                for model in models:
                    print(f"   - {model.get('name', 'Unknown')}")
                return True
            else:
                print(f"❌ Ollama responded with status {response.status_code}")
                return False
    except Exception as e:
        print(f"❌ Ollama connection failed: {e}")
        print("💡 Make sure Ollama is running: ollama serve")
        return False

async def test_database():
    """Test database initialization"""
    print("\n🔍 Testing database...")
    
    try:
        from database.database import init_db, get_db_session, close_db_session
        
        # Initialize database
        success = await init_db()
        if success:
            print("✅ Database initialized successfully")
        else:
            print("❌ Database initialization failed")
            return False
        
        # Test database connection
        db = get_db_session()
        try:
            # Try a simple query
            from database.models import Task
            task_count = db.query(Task).count()
            print(f"✅ Database connection successful (tasks: {task_count})")
            return True
        except Exception as e:
            print(f"❌ Database query failed: {e}")
            return False
        finally:
            close_db_session(db)
            
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def test_file_structure():
    """Test if all required files exist"""
    print("\n🔍 Testing file structure...")
    
    required_files = [
        "main.py",
        "database/database.py",
        "database/models.py",
        "routers/tasks.py",
        "routers/habits.py",
        "routers/ai.py",
        "routers/telegram_bot.py",
        "routers/calendar.py",
        "services/task_service.py",
        "services/habit_service.py",
        "services/ai_service.py",
        "services/telegram_service.py",
        "services/calendar_service.py",
        "services/scheduler.py",
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - Missing")
            all_exist = False
    
    return all_exist

async def test_api_endpoints():
    """Test if the API server can start"""
    print("\n🔍 Testing API server...")
    
    try:
        # Import the FastAPI app
        from main import app
        print("✅ FastAPI app imported successfully")
        
        # Test if we can create the app
        if app:
            print("✅ FastAPI app created successfully")
            return True
        else:
            print("❌ FastAPI app is None")
            return False
            
    except Exception as e:
        print(f"❌ API server test failed: {e}")
        return False

def create_sample_data():
    """Create some sample data for testing"""
    print("\n🔍 Creating sample data...")
    
    try:
        from database.database import get_db_session, close_db_session
        from services.task_service import TaskService
        from services.habit_service import HabitService
        
        db = get_db_session()
        try:
            task_service = TaskService(db)
            habit_service = HabitService(db)
            
            # Create sample tasks
            sample_tasks = [
                {
                    "title": "Welcome to BrainBuddy!",
                    "description": "This is a sample task to get you started.",
                    "priority": "medium"
                },
                {
                    "title": "Try the AI chat feature",
                    "description": "Ask the AI assistant for help with your productivity.",
                    "priority": "low"
                }
            ]
            
            for task_data in sample_tasks:
                task_service.create_task(task_data)
            
            # Create sample habits
            sample_habits = [
                {
                    "name": "Daily Exercise",
                    "description": "Get at least 30 minutes of physical activity",
                    "frequency": "daily"
                },
                {
                    "name": "Read for 20 minutes",
                    "description": "Read books, articles, or educational content",
                    "frequency": "daily"
                }
            ]
            
            for habit_data in sample_habits:
                habit_service.create_habit(habit_data)
            
            print("✅ Sample data created successfully")
            return True
            
        finally:
            close_db_session(db)
            
    except Exception as e:
        print(f"❌ Sample data creation failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("🧠 BrainBuddy Backend Setup Test")
    print("=" * 40)
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    tests = [
        ("Imports", test_imports),
        ("Environment", test_environment),
        ("File Structure", test_file_structure),
        ("Ollama Connection", test_ollama),
        ("Database", test_database),
        ("API Server", test_api_endpoints),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Create sample data if all tests pass
    if all(result for _, result in results):
        print(f"\n{'='*20} Sample Data {'='*20}")
        create_sample_data()
    
    # Summary
    print(f"\n{'='*20} Summary {'='*20}")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nTests passed: {passed}/{total}")
    
    if passed == total:
        print("\n🎉 All tests passed! BrainBuddy backend is ready!")
        print("\n🚀 Next steps:")
        print("1. Start the backend: python main.py")
        print("2. Start the frontend: cd ../frontend && npm start")
        print("3. Open http://localhost:3000 in your browser")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please fix the issues above.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
