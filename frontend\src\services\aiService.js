import api from './api';

export const aiService = {
  // Send message to AI
  async sendMessage(message, context = {}) {
    return await api.post('/ai/chat', {
      message,
      context,
    });
  },

  // Process natural language input
  async processNaturalLanguage(text, context = {}) {
    return await api.post('/ai/process', {
      text,
      context,
    });
  },

  // Get AI suggestions
  async getSuggestions(context = {}) {
    return await api.post('/ai/suggestions', context);
  },

  // Generate weekly plan
  async generateWeeklyPlan(preferences = {}) {
    return await api.post('/ai/weekly-plan', preferences);
  },

  // Get motivational feedback
  async getMotivationalFeedback(context = {}) {
    return await api.post('/ai/motivation', context);
  },

  // Analyze task patterns
  async analyzeTaskPatterns(timeframe = 'week') {
    return await api.get('/ai/analyze', { params: { timeframe } });
  },

  // Get productivity insights
  async getProductivityInsights() {
    return await api.get('/ai/insights');
  },

  // Smart task scheduling
  async scheduleTask(taskData, preferences = {}) {
    return await api.post('/ai/schedule', {
      task: taskData,
      preferences,
    });
  },

  // Get habit recommendations
  async getHabitRecommendations(currentHabits = []) {
    return await api.post('/ai/habits/recommendations', {
      current_habits: currentHabits,
    });
  },

  // Analyze habit progress
  async analyzeHabitProgress(habitId, timeframe = 'month') {
    return await api.get(`/ai/habits/${habitId}/analysis`, {
      params: { timeframe },
    });
  },
};
