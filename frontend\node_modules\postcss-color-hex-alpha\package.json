{"name": "postcss-color-hex-alpha", "description": "Use 4 & 8 character hex color notation in CSS", "version": "8.0.4", "contributors": [{"name": "Antonio <PERSON>", "email": "<EMAIL>", "url": "https://antonio.laguna.es"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>"}], "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "engines": {"node": "^12 || ^14 || >=16"}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs", "default": "./dist/index.mjs"}}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist"], "dependencies": {"postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.4"}, "scripts": {"build": "rollup -c ../../rollup/default.js", "clean": "node -e \"fs.rmSync('./dist', { recursive: true, force: true });\"", "docs": "node ../../.github/bin/generate-docs/install.mjs && node ../../.github/bin/generate-docs/readme.mjs", "lint": "npm run lint:eslint && npm run lint:package-json", "lint:eslint": "eslint ./src --ext .js --ext .ts --ext .mjs --no-error-on-unmatched-pattern", "lint:package-json": "node ../../.github/bin/format-package-json.mjs", "prepublishOnly": "npm run clean && npm run build && npm run test", "test": "node .tape.mjs && npm run test:exports", "test:exports": "node ./test/_import.mjs && node ./test/_require.cjs", "test:rewrite-expects": "REWRITE_EXPECTS=true node .tape.mjs"}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-color-hex-alpha#readme", "repository": {"type": "git", "url": "https://github.com/csstools/postcss-plugins.git", "directory": "plugins/postcss-color-hex-alpha"}, "bugs": "https://github.com/csstools/postcss-plugins/issues", "keywords": ["4-digit", "8-digit", "alpha", "color", "css", "csswg", "hex", "postcss", "postcss-plugin", "spec", "specification", "transparency", "transparent", "w3c"], "csstools": {"cssdbId": "hexadecimal-alpha-notation", "exportName": "postcssColorHexAlpha", "humanReadableName": "PostCSS Color Hex Alpha", "specUrl": "https://www.w3.org/TR/css-color-4/#hex-notation"}, "volta": {"extends": "../../package.json"}}