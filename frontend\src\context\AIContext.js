import React, { createContext, useContext, useReducer } from 'react';
import { aiService } from '../services/aiService';
import toast from 'react-hot-toast';

const AIContext = createContext();

const initialState = {
  messages: [],
  loading: false,
  error: null,
  suggestions: [],
  isThinking: false,
};

function aiReducer(state, action) {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    
    case 'SET_THINKING':
      return { ...state, isThinking: action.payload };
    
    case 'ADD_MESSAGE':
      return { 
        ...state, 
        messages: [...state.messages, action.payload],
        loading: false 
      };
    
    case 'SET_MESSAGES':
      return { ...state, messages: action.payload };
    
    case 'SET_SUGGESTIONS':
      return { ...state, suggestions: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    
    case 'CLEAR_MESSAGES':
      return { ...state, messages: [] };
    
    default:
      return state;
  }
}

export function AI<PERSON>rovider({ children }) {
  const [state, dispatch] = useReducer(aiReducer, initialState);

  const sendMessage = async (message, context = {}) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      // Add user message
      const userMessage = {
        id: Date.now(),
        type: 'user',
        content: message,
        timestamp: new Date().toISOString(),
      };
      dispatch({ type: 'ADD_MESSAGE', payload: userMessage });

      // Get AI response
      const response = await aiService.sendMessage(message, context);
      
      const aiMessage = {
        id: Date.now() + 1,
        type: 'ai',
        content: response.message,
        timestamp: new Date().toISOString(),
        actions: response.actions || [],
      };
      dispatch({ type: 'ADD_MESSAGE', payload: aiMessage });

      return response;
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      toast.error('Failed to get AI response');
      throw error;
    }
  };

  const processNaturalLanguage = async (text, context = {}) => {
    try {
      dispatch({ type: 'SET_THINKING', payload: true });
      const result = await aiService.processNaturalLanguage(text, context);
      dispatch({ type: 'SET_THINKING', payload: false });
      return result;
    } catch (error) {
      dispatch({ type: 'SET_THINKING', payload: false });
      dispatch({ type: 'SET_ERROR', payload: error.message });
      toast.error('Failed to process natural language');
      throw error;
    }
  };

  const getSuggestions = async (context = {}) => {
    try {
      const suggestions = await aiService.getSuggestions(context);
      dispatch({ type: 'SET_SUGGESTIONS', payload: suggestions });
      return suggestions;
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      console.error('Failed to get suggestions:', error);
    }
  };

  const generateWeeklyPlan = async (preferences = {}) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      const plan = await aiService.generateWeeklyPlan(preferences);
      dispatch({ type: 'SET_LOADING', payload: false });
      return plan;
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      toast.error('Failed to generate weekly plan');
      throw error;
    }
  };

  const getMotivationalFeedback = async (context = {}) => {
    try {
      const feedback = await aiService.getMotivationalFeedback(context);
      return feedback;
    } catch (error) {
      console.error('Failed to get motivational feedback:', error);
      return null;
    }
  };

  const clearMessages = () => {
    dispatch({ type: 'CLEAR_MESSAGES' });
  };

  const value = {
    ...state,
    sendMessage,
    processNaturalLanguage,
    getSuggestions,
    generateWeeklyPlan,
    getMotivationalFeedback,
    clearMessages,
  };

  return (
    <AIContext.Provider value={value}>
      {children}
    </AIContext.Provider>
  );
}

export function useAI() {
  const context = useContext(AIContext);
  if (!context) {
    throw new Error('useAI must be used within an AIProvider');
  }
  return context;
}
