import React, { useState, useEffect } from 'react';
import { Plus, Search, Filter, MoreHorizontal } from 'lucide-react';
import { useTask } from '../context/TaskContext';
import TaskItem from './TaskItem';
import TaskForm from './TaskForm';
import { format, isToday, isFuture, isPast } from 'date-fns';

const TaskView = ({ currentView }) => {
  const { tasks, loading, createTask, updateTask, deleteTask } = useTask();
  const [showTaskForm, setShowTaskForm] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTask, setSelectedTask] = useState(null);

  const getViewTitle = () => {
    switch (currentView) {
      case 'inbox': return 'Inbox';
      case 'today': return 'Today';
      case 'planned': return 'Planned';
      case 'completed': return 'Completed';
      default: return 'Tasks';
    }
  };

  const getFilteredTasks = () => {
    let filtered = tasks;

    // Filter by view
    switch (currentView) {
      case 'inbox':
        filtered = tasks.filter(task => 
          task.status === 'pending' && (!task.due_date || task.due_date === null)
        );
        break;
      case 'today':
        filtered = tasks.filter(task => 
          task.status === 'pending' && task.due_date && isToday(new Date(task.due_date))
        );
        break;
      case 'planned':
        filtered = tasks.filter(task => 
          task.status === 'pending' && task.due_date && isFuture(new Date(task.due_date))
        );
        break;
      case 'completed':
        filtered = tasks.filter(task => task.status === 'completed');
        break;
      default:
        filtered = tasks;
    }

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(task =>
        task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        task.description?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return filtered;
  };

  const handleCreateTask = async (taskData) => {
    try {
      await createTask(taskData);
      setShowTaskForm(false);
    } catch (error) {
      console.error('Failed to create task:', error);
    }
  };

  const handleUpdateTask = async (taskId, updates) => {
    try {
      await updateTask(taskId, updates);
      setSelectedTask(null);
    } catch (error) {
      console.error('Failed to update task:', error);
    }
  };

  const handleDeleteTask = async (taskId) => {
    try {
      await deleteTask(taskId);
    } catch (error) {
      console.error('Failed to delete task:', error);
    }
  };

  const filteredTasks = getFilteredTasks();

  return (
    <div className="flex-1 flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-slate-700">
        <div>
          <h1 className="text-2xl font-bold text-white">{getViewTitle()}</h1>
          <p className="text-slate-400 mt-1">
            {filteredTasks.length} {filteredTasks.length === 1 ? 'task' : 'tasks'}
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
            <input
              type="text"
              placeholder="Search tasks..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="input-field pl-10 w-64"
            />
          </div>
          
          {/* Add Task Button */}
          <button
            onClick={() => setShowTaskForm(true)}
            className="btn-primary flex items-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>New Task</span>
          </button>
        </div>
      </div>

      {/* Task List */}
      <div className="flex-1 overflow-y-auto p-6">
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
          </div>
        ) : filteredTasks.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-64 text-slate-400">
            <div className="text-6xl mb-4">📝</div>
            <h3 className="text-lg font-medium mb-2">No tasks found</h3>
            <p className="text-center">
              {searchQuery 
                ? 'Try adjusting your search terms'
                : `No tasks in ${getViewTitle().toLowerCase()} yet. Create your first task!`
              }
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {filteredTasks.map((task) => (
              <TaskItem
                key={task.id}
                task={task}
                onUpdate={handleUpdateTask}
                onDelete={handleDeleteTask}
                onClick={() => setSelectedTask(task)}
              />
            ))}
          </div>
        )}
      </div>

      {/* Task Form Modal */}
      {showTaskForm && (
        <TaskForm
          onSubmit={handleCreateTask}
          onCancel={() => setShowTaskForm(false)}
          defaultStatus={currentView === 'today' ? 'today' : 'inbox'}
        />
      )}

      {/* Task Detail Modal */}
      {selectedTask && (
        <TaskForm
          task={selectedTask}
          onSubmit={(updates) => handleUpdateTask(selectedTask.id, updates)}
          onCancel={() => setSelectedTask(null)}
          isEditing={true}
        />
      )}
    </div>
  );
};

export default TaskView;
