# BrainBuddy Troubleshooting Guide

## 🚨 Common Issues and Solutions

### Installation Issues

#### Node.js Version Problems
**Problem**: "Node.js version not supported"
**Solution**:
```bash
# Check your Node.js version
node --version

# If version is below 18, update Node.js
# Download from https://nodejs.org/
# Or use a version manager like nvm
nvm install 18
nvm use 18
```

#### Python Virtual Environment Issues
**Problem**: Virtual environment creation fails
**Solution**:
```bash
# Make sure Python 3.9+ is installed
python3 --version

# Try creating venv with different commands
python3 -m venv backend/venv
# OR
python -m venv backend/venv

# If still failing, install venv module
pip install virtualenv
virtualenv backend/venv
```

#### Permission Errors (Linux/Mac)
**Problem**: Permission denied when running scripts
**Solution**:
```bash
# Make scripts executable
chmod +x setup.sh
chmod +x start.sh

# If still having issues, run with sudo (not recommended)
sudo ./setup.sh
```

### Ollama Issues

#### Ollama Not Running
**Problem**: "Failed to connect to Ollama"
**Solution**:
```bash
# Start Ollama service
ollama serve

# In another terminal, check if it's running
curl http://localhost:11434/api/tags

# If port is busy, kill existing process
pkill ollama
ollama serve
```

#### Model Not Found
**Problem**: "Model 'mistral' not found"
**Solution**:
```bash
# Pull the mistral model
ollama pull mistral

# List available models
ollama list

# If mistral is not working, try llama2
ollama pull llama2
# Then update .env file: OLLAMA_MODEL=llama2
```

#### Slow AI Responses
**Problem**: AI takes too long to respond
**Solution**:
1. **Use a smaller model**:
   ```bash
   ollama pull llama2:7b
   # Update .env: OLLAMA_MODEL=llama2:7b
   ```

2. **Increase system resources**:
   - Close other applications
   - Ensure sufficient RAM (8GB+ recommended)
   - Use SSD storage for better performance

3. **Check system load**:
   ```bash
   # Monitor system resources
   top
   htop
   ```

### Backend Issues

#### Database Connection Errors
**Problem**: "Database connection failed"
**Solution**:
```bash
# Delete and recreate database
cd backend
rm brainbuddy.db
python main.py  # Will recreate tables

# Check database permissions
ls -la brainbuddy.db
chmod 664 brainbuddy.db
```

#### Import Errors
**Problem**: "ModuleNotFoundError"
**Solution**:
```bash
# Activate virtual environment
cd backend
source venv/bin/activate  # Linux/Mac
# OR
venv\Scripts\activate     # Windows

# Reinstall dependencies
pip install -r requirements.txt

# Check Python path
python -c "import sys; print(sys.path)"
```

#### Port Already in Use
**Problem**: "Port 8000 is already in use"
**Solution**:
```bash
# Find process using port 8000
lsof -i :8000          # Linux/Mac
netstat -ano | findstr :8000  # Windows

# Kill the process
kill -9 <PID>          # Linux/Mac
taskkill /PID <PID> /F # Windows

# Or use a different port
# Update .env: API_PORT=8001
```

### Frontend Issues

#### React App Won't Start
**Problem**: Frontend fails to start
**Solution**:
```bash
# Clear node modules and reinstall
cd frontend
rm -rf node_modules package-lock.json
npm install

# Clear npm cache
npm cache clean --force

# Try different port
PORT=3001 npm start
```

#### Electron App Issues
**Problem**: Electron app doesn't launch
**Solution**:
```bash
# Check if Electron is installed
npm list electron

# Reinstall Electron
npm install electron --save-dev

# Try running directly
npx electron frontend/main.js

# Check for conflicting processes
ps aux | grep electron
```

#### Build Errors
**Problem**: "Build failed" or compilation errors
**Solution**:
```bash
# Clear build cache
cd frontend
rm -rf build
npm run build

# Check for syntax errors in components
npm run lint

# Update dependencies
npm update
```

### API Connection Issues

#### CORS Errors
**Problem**: "CORS policy blocked the request"
**Solution**:
1. **Check backend CORS settings** in `backend/main.py`:
   ```python
   app.add_middleware(
       CORSMiddleware,
       allow_origins=["http://localhost:3000"],
       allow_credentials=True,
       allow_methods=["*"],
       allow_headers=["*"],
   )
   ```

2. **Verify frontend API URL** in `frontend/src/services/api.js`:
   ```javascript
   const API_BASE_URL = 'http://localhost:8000';
   ```

#### Network Timeout
**Problem**: API requests timeout
**Solution**:
```bash
# Check if backend is running
curl http://localhost:8000/health

# Increase timeout in frontend
# Edit frontend/src/services/api.js
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // Increase to 30 seconds
});
```

### Telegram Bot Issues

#### Bot Not Responding
**Problem**: Telegram bot doesn't respond to commands
**Solution**:
1. **Check bot token**:
   ```bash
   # Verify token in .env file
   cat .env | grep TELEGRAM_BOT_TOKEN
   
   # Test token manually
   curl "https://api.telegram.org/bot<YOUR_TOKEN>/getMe"
   ```

2. **Check webhook settings**:
   ```bash
   # Delete existing webhook
   curl -X POST http://localhost:8000/telegram/delete-webhook
   
   # For local development, use polling instead of webhook
   ```

3. **Verify chat ID**:
   ```bash
   # Get your chat ID by messaging the bot and checking:
   curl "https://api.telegram.org/bot<YOUR_TOKEN>/getUpdates"
   ```

#### Webhook Issues
**Problem**: Webhook not receiving updates
**Solution**:
```bash
# For local development, don't use webhooks
# Comment out webhook setup in telegram_service.py

# For production, ensure HTTPS and valid domain
# Set webhook URL to: https://yourdomain.com/telegram/webhook
```

### Performance Issues

#### High Memory Usage
**Problem**: Application uses too much RAM
**Solution**:
1. **Reduce AI model size**:
   ```bash
   ollama pull llama2:7b  # Smaller model
   ```

2. **Limit database query results**:
   ```python
   # In service files, add limits
   tasks = query.limit(100).all()
   ```

3. **Clear browser cache**:
   - Open DevTools (F12)
   - Right-click refresh button
   - Select "Empty Cache and Hard Reload"

#### Slow Database Queries
**Problem**: Database operations are slow
**Solution**:
```sql
-- Add indexes to frequently queried columns
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_due_date ON tasks(due_date);
CREATE INDEX idx_habits_created_at ON habits(created_at);
```

### Development Issues

#### Hot Reload Not Working
**Problem**: Changes don't reflect automatically
**Solution**:
```bash
# Frontend hot reload
cd frontend
rm -rf node_modules/.cache
npm start

# Backend auto-reload
cd backend
uvicorn main:app --reload --host localhost --port 8000
```

#### Environment Variables Not Loading
**Problem**: .env variables not recognized
**Solution**:
```bash
# Check .env file location (should be in project root)
ls -la .env

# Verify .env format (no spaces around =)
# Correct: API_PORT=8000
# Wrong:   API_PORT = 8000

# Restart application after .env changes
```

## 🔧 Diagnostic Commands

### System Health Check
```bash
# Run backend tests
cd backend
source venv/bin/activate
python test_setup.py

# Run frontend tests
node test_frontend.js

# Check all services
curl http://localhost:8000/health
curl http://localhost:3000
curl http://localhost:11434/api/tags
```

### Log Analysis
```bash
# Backend logs
cd backend
python main.py 2>&1 | tee backend.log

# Frontend logs (check browser console)
# Open DevTools (F12) -> Console tab

# Ollama logs
ollama logs
```

### Database Inspection
```bash
# Connect to SQLite database
cd backend
sqlite3 brainbuddy.db

# Check tables
.tables

# Check table structure
.schema tasks

# Count records
SELECT COUNT(*) FROM tasks;
```

## 🆘 Getting Help

### Before Asking for Help
1. **Check this troubleshooting guide**
2. **Run diagnostic commands**
3. **Check logs for error messages**
4. **Try restarting all services**
5. **Verify system requirements**

### Where to Get Help
- **GitHub Issues**: Create a detailed issue report
- **Documentation**: Check README.md and FEATURES.md
- **Community**: Join discussions on GitHub

### Creating a Good Bug Report
Include:
1. **System information**: OS, Node.js version, Python version
2. **Steps to reproduce**: Exact steps that cause the issue
3. **Expected behavior**: What should happen
4. **Actual behavior**: What actually happens
5. **Error messages**: Full error text and stack traces
6. **Logs**: Relevant log entries
7. **Configuration**: .env file (without sensitive data)

### Emergency Reset
If everything is broken:
```bash
# Nuclear option - reset everything
rm -rf node_modules frontend/node_modules backend/venv backend/brainbuddy.db
./setup.sh  # Run setup again
```

## 📋 Maintenance

### Regular Maintenance Tasks
```bash
# Update dependencies monthly
npm update
cd frontend && npm update
cd ../backend && source venv/bin/activate && pip list --outdated

# Clean up database
cd backend
sqlite3 brainbuddy.db "VACUUM;"

# Update Ollama models
ollama pull mistral
```

### Backup Your Data
```bash
# Backup database
cp backend/brainbuddy.db backup/brainbuddy_$(date +%Y%m%d).db

# Export tasks to JSON
curl http://localhost:8000/tasks > backup/tasks_$(date +%Y%m%d).json
```
