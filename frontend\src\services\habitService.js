import api from './api';

export const habitService = {
  // Get all habits
  async getHabits() {
    return await api.get('/habits');
  },

  // Get habit by ID
  async getHabit(id) {
    return await api.get(`/habits/${id}`);
  },

  // Create new habit
  async createHabit(habitData) {
    return await api.post('/habits', habitData);
  },

  // Update habit
  async updateHabit(id, updates) {
    return await api.put(`/habits/${id}`, updates);
  },

  // Delete habit
  async deleteHabit(id) {
    return await api.delete(`/habits/${id}`);
  },

  // Mark habit as completed for today
  async completeHabit(id) {
    return await api.post(`/habits/${id}/complete`);
  },

  // Get habit progress
  async getHabitProgress(id, timeframe = 'month') {
    return await api.get(`/habits/${id}/progress`, {
      params: { timeframe },
    });
  },

  // Get habit statistics
  async getHabitStats(id) {
    return await api.get(`/habits/${id}/stats`);
  },

  // Get all habits progress
  async getAllHabitsProgress(timeframe = 'week') {
    return await api.get('/habits/progress', {
      params: { timeframe },
    });
  },

  // Reset habit streak
  async resetHabitStreak(id) {
    return await api.post(`/habits/${id}/reset-streak`);
  },

  // Get habit completion history
  async getHabitHistory(id, limit = 30) {
    return await api.get(`/habits/${id}/history`, {
      params: { limit },
    });
  },
};
