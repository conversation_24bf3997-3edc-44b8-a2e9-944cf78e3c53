import React, { useState, useRef, useEffect } from 'react';
import { Send, Bot, User, <PERSON>rk<PERSON>, Lightbulb, Zap } from 'lucide-react';
import { useAI } from '../context/AIContext';
import { useTask } from '../context/TaskContext';
import { format } from 'date-fns';

const ChatView = () => {
  const { messages, sendMessage, loading, suggestions, getSuggestions } = useAI();
  const { tasks } = useTask();
  const [input, setInput] = useState('');
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // Get initial suggestions when component mounts
    getSuggestions({ tasks: tasks.slice(0, 5) });
  }, [tasks]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!input.trim() || loading) return;

    const message = input.trim();
    setInput('');

    try {
      await sendMessage(message, {
        tasks: tasks.slice(0, 10), // Send recent tasks for context
        current_time: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  const handleSuggestionClick = (suggestion) => {
    setInput(suggestion);
    inputRef.current?.focus();
  };

  const quickActions = [
    { text: "What should I focus on today?", icon: Zap },
    { text: "Create a task for tomorrow", icon: Sparkles },
    { text: "Show my productivity insights", icon: Lightbulb },
    { text: "Plan my week", icon: Bot },
  ];

  return (
    <div className="flex flex-col h-full bg-slate-900">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-slate-700">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center">
            <Bot className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-xl font-bold text-white">AI Assistant</h1>
            <p className="text-slate-400 text-sm">Your personal productivity companion</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2 text-sm text-slate-400">
          <div className="w-2 h-2 bg-green-400 rounded-full"></div>
          <span>Online</span>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-6 space-y-4">
        {messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <div className="w-16 h-16 bg-primary-600/20 rounded-full flex items-center justify-center mb-4">
              <Bot className="w-8 h-8 text-primary-400" />
            </div>
            <h3 className="text-lg font-medium text-white mb-2">
              Hello! I'm your AI assistant
            </h3>
            <p className="text-slate-400 mb-6 max-w-md">
              I can help you manage tasks, plan your day, provide insights, and much more. 
              Try asking me something or use one of the quick actions below.
            </p>
            
            {/* Quick Actions */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 w-full max-w-md">
              {quickActions.map((action, index) => {
                const Icon = action.icon;
                return (
                  <button
                    key={index}
                    onClick={() => handleSuggestionClick(action.text)}
                    className="flex items-center space-x-3 p-3 bg-slate-800 hover:bg-slate-700 rounded-lg border border-slate-600 transition-colors text-left"
                  >
                    <Icon className="w-5 h-5 text-primary-400 flex-shrink-0" />
                    <span className="text-slate-300 text-sm">{action.text}</span>
                  </button>
                );
              })}
            </div>
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div className={`flex items-start space-x-3 max-w-3xl ${
                  message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''
                }`}>
                  {/* Avatar */}
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                    message.type === 'user' 
                      ? 'bg-primary-600' 
                      : 'bg-slate-700'
                  }`}>
                    {message.type === 'user' ? (
                      <User className="w-4 h-4 text-white" />
                    ) : (
                      <Bot className="w-4 h-4 text-slate-300" />
                    )}
                  </div>

                  {/* Message Content */}
                  <div className={`rounded-lg p-4 ${
                    message.type === 'user'
                      ? 'bg-primary-600 text-white'
                      : 'bg-slate-800 text-slate-200'
                  }`}>
                    <p className="whitespace-pre-wrap">{message.content}</p>
                    
                    {/* Actions (for AI messages) */}
                    {message.actions && message.actions.length > 0 && (
                      <div className="mt-3 space-y-2">
                        {message.actions.map((action, index) => (
                          <button
                            key={index}
                            onClick={() => {
                              // Handle action click
                              console.log('Action clicked:', action);
                            }}
                            className="block w-full text-left p-2 bg-slate-700 hover:bg-slate-600 rounded text-sm transition-colors"
                          >
                            {action.label}
                          </button>
                        ))}
                      </div>
                    )}
                    
                    <div className="text-xs opacity-70 mt-2">
                      {format(new Date(message.timestamp), 'HH:mm')}
                    </div>
                  </div>
                </div>
              </div>
            ))}
            
            {loading && (
              <div className="flex justify-start">
                <div className="flex items-start space-x-3 max-w-3xl">
                  <div className="w-8 h-8 bg-slate-700 rounded-full flex items-center justify-center">
                    <Bot className="w-4 h-4 text-slate-300" />
                  </div>
                  <div className="bg-slate-800 rounded-lg p-4">
                    <div className="flex items-center space-x-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-500"></div>
                      <span className="text-slate-400">AI is thinking...</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Suggestions */}
      {suggestions.length > 0 && messages.length > 0 && (
        <div className="px-6 py-3 border-t border-slate-700">
          <div className="flex items-center space-x-2 mb-2">
            <Lightbulb className="w-4 h-4 text-yellow-400" />
            <span className="text-sm text-slate-400">Suggestions:</span>
          </div>
          <div className="flex flex-wrap gap-2">
            {suggestions.slice(0, 3).map((suggestion, index) => (
              <button
                key={index}
                onClick={() => handleSuggestionClick(suggestion)}
                className="px-3 py-1 bg-slate-800 hover:bg-slate-700 rounded-full text-sm text-slate-300 border border-slate-600 transition-colors"
              >
                {suggestion}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Input */}
      <div className="p-6 border-t border-slate-700">
        <form onSubmit={handleSubmit} className="flex space-x-3">
          <input
            ref={inputRef}
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Ask me anything about your tasks, productivity, or planning..."
            className="flex-1 input-field"
            disabled={loading}
          />
          <button
            type="submit"
            disabled={loading || !input.trim()}
            className="btn-primary px-4 py-2 flex items-center justify-center"
          >
            <Send className="w-4 h-4" />
          </button>
        </form>
      </div>
    </div>
  );
};

export default ChatView;
