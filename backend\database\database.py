import os
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
from dotenv import load_dotenv

load_dotenv()

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./brainbuddy.db")

# Create engine
if DATABASE_URL.startswith("sqlite"):
    engine = create_engine(
        DATABASE_URL,
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
        echo=False
    )
else:
    engine = create_engine(DATABASE_URL, echo=False)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create base class for models
Base = declarative_base()

# Dependency to get database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Initialize database
async def init_db():
    """Initialize database and create tables"""
    try:
        # Import all models to ensure they are registered
        from database.models import Task, Habit, Event, HabitCompletion
        
        # Create all tables
        Base.metadata.create_all(bind=engine)
        
        print("Database tables created successfully")
        return True
    except Exception as e:
        print(f"Error initializing database: {e}")
        return False

# Database utilities
def get_db_session():
    """Get a database session for synchronous operations"""
    return SessionLocal()

def close_db_session(db):
    """Close a database session"""
    db.close()
