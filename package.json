{"name": "brainbuddy", "version": "1.0.0", "description": "AI-powered personal assistant with task management and Telegram integration", "main": "frontend/main.js", "scripts": {"dev": "concurrently \"npm run electron:dev\" \"npm run react:dev\"", "electron:dev": "electron frontend/main.js", "react:dev": "cd frontend && npm run dev", "build": "cd frontend && npm run build", "start": "electron .", "pack": "electron-builder", "dist": "electron-builder --publish=never"}, "keywords": ["ai", "task-management", "personal-assistant", "telegram", "electron"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"electron-is-dev": "^2.0.0"}, "build": {"appId": "com.brainbuddy.app", "productName": "<PERSON><PERSON><PERSON><PERSON>", "directories": {"output": "dist"}, "files": ["frontend/build/**/*", "frontend/main.js", "node_modules/**/*"]}}